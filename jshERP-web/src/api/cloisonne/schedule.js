/**
 * 掐丝珐琅馆排班管理API接口
 * 基于销售管理模块的API设计模式
 */
import { getAction, postAction, putAction, deleteAction } from '@/api/manage'

// API路径前缀
const API_PREFIX = '/cloisonne/schedule'

/**
 * 获取排班列表
 * @param params 查询参数
 */
export const getScheduleList = (params) => {
  return getAction(`${API_PREFIX}/list`, params)
}

/**
 * 获取排班详情
 * @param id 排班ID
 */
export const getScheduleDetail = (id) => {
  return getAction(`${API_PREFIX}/detail`, { id })
}

/**
 * 新增排班
 * @param data 排班数据
 */
export const addSchedule = (data) => {
  return postAction(`${API_PREFIX}/add`, data)
}

/**
 * 更新排班
 * @param data 排班数据
 */
export const updateSchedule = (data) => {
  return putAction(`${API_PREFIX}/update`, data)
}

/**
 * 删除排班
 * @param id 排班ID
 */
export const deleteSchedule = (id) => {
  return deleteAction(`${API_PREFIX}/delete`, { id })
}

/**
 * 批量删除排班
 * @param ids 排班ID数组
 */
export const batchDeleteSchedule = (ids) => {
  return deleteAction(`${API_PREFIX}/deleteBatch`, { ids: ids.join(',') })
}

/**
 * 批量新增排班
 * @param schedules 排班数据数组
 */
export const batchAddSchedule = (schedules) => {
  return postAction(`${API_PREFIX}/batchAdd`, { schedules })
}

/**
 * 批量更新排班状态
 * @param data 包含ids和status的对象
 */
export const batchUpdateScheduleStatus = (data) => {
  return postAction(`${API_PREFIX}/batchSetStatus`, data)
}

/**
 * 检查排班冲突
 * @param params 冲突检测参数
 */
export const checkScheduleConflict = (params) => {
  return postAction(`${API_PREFIX}/checkConflict`, params)
}

/**
 * 获取排班统计数据
 * @param params 统计参数
 */
export const getScheduleStatistics = (params) => {
  return getAction(`${API_PREFIX}/statistics`, params)
}

/**
 * 获取员工列表
 */
export const getEmployees = () => {
  return getAction(`${API_PREFIX}/employees`)
}

/**
 * 获取员工排班历史
 * @param employeeId 员工ID
 * @param params 查询参数
 */
export const getEmployeeScheduleHistory = (employeeId, params) => {
  return getAction(`${API_PREFIX}/employee/${employeeId}/history`, params)
}

/**
 * 获取班次类型列表
 */
export const getShiftTypes = () => {
  return getAction(`${API_PREFIX}/shiftTypes`)
}

/**
 * 获取工作区域列表
 */
export const getWorkAreas = () => {
  return getAction(`${API_PREFIX}/workAreas`)
}

/**
 * 导出排班数据
 * @param params 导出参数
 */
export const exportScheduleData = (params) => {
  return getAction(`${API_PREFIX}/export`, params)
}

/**
 * 导入排班数据
 * @param file 导入文件
 */
export const importScheduleData = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return postAction(`${API_PREFIX}/import`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取排班模板
 */
export const getScheduleTemplate = () => {
  return getAction(`${API_PREFIX}/template`)
}

/**
 * 保存排班模板
 * @param template 模板数据
 */
export const saveScheduleTemplate = (template) => {
  return postAction(`${API_PREFIX}/template`, template)
}

/**
 * 复制排班
 * @param sourceDate 源日期
 * @param targetDate 目标日期
 * @param options 复制选项
 */
export const copySchedule = (sourceDate, targetDate, options = {}) => {
  return postAction(`${API_PREFIX}/copy`, {
    sourceDate,
    targetDate,
    ...options
  })
}

/**
 * 调班申请
 * @param data 调班数据
 */
export const applyScheduleSwap = (data) => {
  return postAction(`${API_PREFIX}/swap/apply`, data)
}

/**
 * 审批调班申请
 * @param swapId 调班申请ID
 * @param approved 是否批准
 * @param reason 审批原因
 */
export const approveScheduleSwap = (swapId, approved, reason) => {
  return postAction(`${API_PREFIX}/swap/approve`, {
    swapId,
    approved,
    reason
  })
}

/**
 * 获取调班申请列表
 * @param params 查询参数
 */
export const getScheduleSwapList = (params) => {
  return getAction(`${API_PREFIX}/swap/list`, params)
}

/**
 * 请假申请
 * @param data 请假数据
 */
export const applyLeave = (data) => {
  return postAction(`${API_PREFIX}/leave/apply`, data)
}

/**
 * 审批请假申请
 * @param leaveId 请假申请ID
 * @param approved 是否批准
 * @param reason 审批原因
 */
export const approveLeave = (leaveId, approved, reason) => {
  return postAction(`${API_PREFIX}/leave/approve`, {
    leaveId,
    approved,
    reason
  })
}

/**
 * 获取请假申请列表
 * @param params 查询参数
 */
export const getLeaveList = (params) => {
  return getAction(`${API_PREFIX}/leave/list`, params)
}

/**
 * 获取员工工时统计
 * @param params 统计参数
 */
export const getEmployeeWorkHours = (params) => {
  return getAction(`${API_PREFIX}/workHours`, params)
}

/**
 * 获取排班日历数据
 * @param params 日历参数
 */
export const getScheduleCalendar = (params) => {
  return getAction(`${API_PREFIX}/calendar`, params)
}

/**
 * 自动排班
 * @param params 自动排班参数
 */
export const autoSchedule = (params) => {
  return postAction(`${API_PREFIX}/auto`, params)
}

/**
 * 获取排班建议
 * @param params 建议参数
 */
export const getScheduleSuggestions = (params) => {
  return getAction(`${API_PREFIX}/suggestions`, params)
}

/**
 * 发送排班通知
 * @param scheduleIds 排班ID数组
 * @param notificationType 通知类型
 */
export const sendScheduleNotification = (scheduleIds, notificationType) => {
  return postAction(`${API_PREFIX}/notify`, {
    scheduleIds,
    notificationType
  })
}

/**
 * 获取排班配置
 */
export const getScheduleConfig = () => {
  return getAction(`${API_PREFIX}/config`)
}

/**
 * 更新排班配置
 * @param config 配置数据
 */
export const updateScheduleConfig = (config) => {
  return putAction(`${API_PREFIX}/config`, config)
}

// 导出所有API方法
export default {
  getScheduleList,
  getScheduleDetail,
  addSchedule,
  updateSchedule,
  deleteSchedule,
  batchDeleteSchedule,
  batchAddSchedule,
  batchUpdateScheduleStatus,
  checkScheduleConflict,
  getScheduleStatistics,
  getEmployees,
  getEmployeeScheduleHistory,
  getShiftTypes,
  getWorkAreas,
  exportScheduleData,
  importScheduleData,
  getScheduleTemplate,
  saveScheduleTemplate,
  copySchedule,
  applyScheduleSwap,
  approveScheduleSwap,
  getScheduleSwapList,
  applyLeave,
  approveLeave,
  getLeaveList,
  getEmployeeWorkHours,
  getScheduleCalendar,
  autoSchedule,
  getScheduleSuggestions,
  sendScheduleNotification,
  getScheduleConfig,
  updateScheduleConfig
}
