<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :bordered="false">
        <!-- 页面标题和配置状态总览 -->
        <div class="config-header">
          <h2>薪酬系统配置中心</h2>
          <p class="config-description">统一管理薪酬系统的所有配置规则，为薪酬计算提供基础支持</p>

          <!-- 配置状态总览 -->
          <a-row :gutter="24" style="margin: 24px 0;">
            <a-col :span="8">
              <a-card size="small" class="status-card">
                <a-statistic
                  title="个人薪酬规则"
                  :value="personalRulesStatus.configured"
                  :total="personalRulesStatus.total"
                  suffix="/ 项已配置"
                  :value-style="{ color: personalRulesStatus.configured === personalRulesStatus.total ? '#3f8600' : '#cf1322' }"
                />
                <div class="status-actions">
                  <a-button size="small" type="primary" @click="handlePersonalRulesConfig">配置管理</a-button>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="status-card">
                <a-statistic
                  title="系统自动匹配规则"
                  :value="systemRulesStatus.configured"
                  :total="systemRulesStatus.total"
                  suffix="/ 项已配置"
                  :value-style="{ color: systemRulesStatus.configured === systemRulesStatus.total ? '#3f8600' : '#cf1322' }"
                />
                <div class="status-actions">
                  <a-button size="small" type="primary" @click="handleSystemRulesConfig">配置管理</a-button>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="status-card">
                <a-statistic
                  title="填报规则"
                  :value="manualRulesStatus.configured"
                  :total="manualRulesStatus.total"
                  suffix="/ 项已配置"
                  :value-style="{ color: manualRulesStatus.configured === manualRulesStatus.total ? '#3f8600' : '#cf1322' }"
                />
                <div class="status-actions">
                  <a-button size="small" type="primary" @click="handleManualRulesConfig">配置管理</a-button>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
        <!-- 个人薪酬规则配置区域 -->
        <a-card title="个人薪酬规则配置" :bordered="false" style="margin-bottom: 24px;">
          <div class="config-section-description">
            <a-alert
              message="个人薪酬规则配置"
              description="管理员工可选择的薪酬项目模板，设置默认值，并提供批量配置工具。这些配置将在员工薪酬档案中使用。"
              type="info"
              show-icon
              style="margin-bottom: 16px"
            />
          </div>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleSalaryItemTemplates">
                <div class="function-card-content">
                  <a-icon type="profile" class="function-icon" />
                  <h4>薪酬项目模板</h4>
                  <p>管理可用的薪酬项目类型，设置计算方式和默认参数</p>
                  <div class="function-status">
                    <a-tag color="green">{{ salaryItemTemplates.length }} 个模板</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleDefaultSettings">
                <div class="function-card-content">
                  <a-icon type="setting" class="function-icon" />
                  <h4>默认值设置</h4>
                  <p>为新员工设置默认的薪酬项目配置和参数</p>
                  <div class="function-status">
                    <a-tag color="blue">{{ defaultSettings.enabledItems }} 项默认启用</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleBatchConfig">
                <div class="function-card-content">
                  <a-icon type="team" class="function-icon" />
                  <h4>批量配置工具</h4>
                  <p>按部门或条件批量设置员工薪酬规则</p>
                  <div class="function-status">
                    <a-tag color="orange">快速配置</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>

        <!-- 系统自动匹配规则配置区域 -->
        <a-card title="系统自动匹配规则配置" :bordered="false" style="margin-bottom: 24px;">
          <div class="config-section-description">
            <a-alert
              message="系统自动匹配规则配置"
              description="配置系统自动计算的薪酬项目，如生产提成等。这些规则将自动从相关业务模块获取数据并计算薪酬。"
              type="warning"
              show-icon
              style="margin-bottom: 16px"
            />
          </div>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleProductionRules">
                <div class="function-card-content">
                  <a-icon type="tool" class="function-icon" />
                  <h4>生产提成规则</h4>
                  <p>配置掐丝点蓝制作费、配饰制作费等生产相关提成</p>
                  <div class="function-status">
                    <a-tag color="green">{{ productionRules.length }} 项规则</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleMatchingLogic">
                <div class="function-card-content">
                  <a-icon type="branches" class="function-icon" />
                  <h4>匹配逻辑设置</h4>
                  <p>设置数据获取规则和自动匹配逻辑</p>
                  <div class="function-status">
                    <a-tag color="blue">自动匹配</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleCalculationEngine">
                <div class="function-card-content">
                  <a-icon type="thunderbolt" class="function-icon" />
                  <h4>计算引擎参数</h4>
                  <p>配置计算频率、数据同步和异常处理规则</p>
                  <div class="function-status">
                    <a-tag color="orange">引擎配置</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>

        <!-- 填报规则管理区域 -->
        <a-card title="填报规则管理" :bordered="false" style="margin-bottom: 24px;">
          <div class="config-section-description">
            <a-alert
              message="填报规则管理"
              description="管理需要手动填报的薪酬项目，如报销等。配置报销类型、审批流程和相关规则。"
              type="success"
              show-icon
              style="margin-bottom: 16px"
            />
          </div>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleReimbursementTypes">
                <div class="function-card-content">
                  <a-icon type="file-text" class="function-icon" />
                  <h4>报销类型管理</h4>
                  <p>定义可用的报销类型和相关规则</p>
                  <div class="function-status">
                    <a-tag color="green">{{ reimbursementTypes.length }} 种类型</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleApprovalProcess">
                <div class="function-card-content">
                  <a-icon type="audit" class="function-icon" />
                  <h4>审批流程配置</h4>
                  <p>设置报销审批层级和自动审批规则</p>
                  <div class="function-status">
                    <a-tag color="blue">{{ approvalProcess.levels }} 级审批</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="function-card" @click="handleReimbursementManagement">
                <div class="function-card-content">
                  <a-icon type="link" class="function-icon" />
                  <h4>报销管理入口</h4>
                  <p>快速跳转到报销申请和审批管理页面</p>
                  <div class="function-status">
                    <a-tag color="orange">管理入口</a-tag>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </a-card>

        <!-- 系统全局配置区域（折叠面板） -->
        <a-collapse style="margin-bottom: 24px;">
          <a-collapse-panel key="global" header="系统全局配置" :show-arrow="true">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card size="small" title="薪酬计算配置">
                  <a-form layout="vertical">
                    <a-form-item label="默认工作天数/月">
                      <a-input-number
                        v-model="systemConfig.defaultWorkDays"
                        :min="1"
                        :max="31"
                        style="width: 100%;" />
                    </a-form-item>
                    <a-form-item label="计算超时时间(分钟)">
                      <a-input-number
                        v-model="systemConfig.calculationTimeout"
                        :min="1"
                        :max="120"
                        style="width: 100%;" />
                    </a-form-item>
                    <a-form-item label="批量计算大小">
                      <a-input-number
                        v-model="systemConfig.batchSize"
                        :min="10"
                        :max="1000"
                        style="width: 100%;" />
                    </a-form-item>
                  </a-form>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card size="small" title="系统权限配置">
                  <a-form layout="vertical">
                    <a-form-item label="默认数据权限级别">
                      <a-radio-group v-model="systemConfig.defaultDataLevel">
                        <a-radio value="PERSONAL">个人权限</a-radio>
                        <a-radio value="DEPARTMENT">部门权限</a-radio>
                        <a-radio value="ALL">全部权限</a-radio>
                      </a-radio-group>
                    </a-form-item>
                    <a-form-item label="操作权限">
                      <a-checkbox-group v-model="systemConfig.operationPermissions">
                        <a-checkbox value="CALCULATE_SALARY">执行薪酬计算</a-checkbox>
                        <a-checkbox value="APPROVE_SALARY">审批薪酬计算</a-checkbox>
                        <a-checkbox value="PAY_SALARY">执行薪酬发放</a-checkbox>
                        <a-checkbox value="CONFIG_SYSTEM">系统配置管理</a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                  </a-form>
                </a-card>
              </a-col>
            </a-row>
            <div style="text-align: center; margin-top: 16px;">
              <a-button type="primary" @click="handleSaveGlobalConfig">保存全局配置</a-button>
              <a-button @click="handleResetGlobalConfig" style="margin-left: 8px;">重置配置</a-button>
            </div>
          </a-collapse-panel>
        </a-collapse>

        <!-- 薪酬项目模板管理弹窗 -->
        <salary-item-template-modal
          :visible="itemTemplateModalVisible"
          @ok="handleItemTemplateModalOk"
          @cancel="handleItemTemplateModalCancel" />

        <!-- 生产提成规则配置弹窗 -->
        <salary-config-modal
          :visible="productionRulesModalVisible"
          @ok="handleProductionRulesModalOk"
          @cancel="handleProductionRulesModalCancel" />

        <!-- 报销类型管理弹窗 -->
        <reimbursement-type-modal
          :visible="reimbursementTypeModalVisible"
          @ok="handleReimbursementTypeModalOk"
          @cancel="handleReimbursementTypeModalCancel" />

        <!-- 计算引擎参数配置弹窗 -->
        <calculation-engine-modal
          :visible="calculationEngineModalVisible"
          @ok="handleCalculationEngineModalOk"
          @cancel="handleCalculationEngineModalCancel" />

        <!-- 数据匹配逻辑设置弹窗 -->
        <matching-logic-modal
          :visible="matchingLogicModalVisible"
          @ok="handleMatchingLogicModalOk"
          @cancel="handleMatchingLogicModalCancel" />

        <!-- 默认值设置弹窗 -->
        <default-settings-modal
          :visible="defaultSettingsModalVisible"
          @ok="handleDefaultSettingsModalOk"
          @cancel="handleDefaultSettingsModalCancel" />

        <!-- 批量配置工具弹窗 -->
        <batch-config-modal
          :visible="batchConfigModalVisible"
          @ok="handleBatchConfigModalOk"
          @cancel="handleBatchConfigModalCancel" />

        <!-- 审批流程配置弹窗 -->
        <approval-process-modal
          :visible="approvalProcessModalVisible"
          @ok="handleApprovalProcessModalOk"
          @cancel="handleApprovalProcessModalCancel" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { getSalaryItemList, addSalaryItem, updateSalaryItem, updateSalaryItemStatus } from '@/api/salary'
import SalaryConfigModal from './components/SalaryConfigModal'
import SalaryItemTemplateModal from './components/SalaryItemTemplateModal'
import ReimbursementTypeModal from './components/ReimbursementTypeModal'
import CalculationEngineModal from './components/CalculationEngineModal'
import MatchingLogicModal from './components/MatchingLogicModal'
import DefaultSettingsModal from './components/DefaultSettingsModal'
import BatchConfigModal from './components/BatchConfigModal'
import ApprovalProcessModal from './components/ApprovalProcessModal'

export default {
  name: 'SalaryConfig',
  components: {
    SalaryConfigModal,
    SalaryItemTemplateModal,
    ReimbursementTypeModal,
    CalculationEngineModal,
    MatchingLogicModal,
    DefaultSettingsModal,
    BatchConfigModal,
    ApprovalProcessModal
  },
  data() {
    return {
      // 配置状态统计
      personalRulesStatus: {
        configured: 8,
        total: 9
      },
      systemRulesStatus: {
        configured: 2,
        total: 3
      },
      manualRulesStatus: {
        configured: 4,
        total: 5
      },

      // 个人薪酬规则配置数据
      salaryItemTemplates: [
        { code: 'DAILY_WAGE', name: '基础日薪', type: 'FIXED', status: 'ACTIVE' },
        { code: 'COFFEE_COMMISSION', name: '咖啡店提成', type: 'COMMISSION', status: 'ACTIVE' },
        { code: 'INSTRUCTOR_FEE', name: '讲师费', type: 'PROJECT', status: 'ACTIVE' },
        { code: 'ASSISTANT_FEE', name: '助理费', type: 'PROJECT', status: 'ACTIVE' },
        { code: 'CHANNEL_COMMISSION', name: '渠道开发提成', type: 'COMMISSION', status: 'ACTIVE' },
        { code: 'HALL_SALES', name: '馆内销售提成', type: 'COMMISSION', status: 'ACTIVE' },
        { code: 'ARTWORK_SALES', name: '艺术作品销售', type: 'MANUAL', status: 'ACTIVE' }
      ],

      defaultSettings: {
        enabledItems: 3
      },

      // 系统自动匹配规则数据
      productionRules: [
        { code: 'CLOISONNE_PRODUCTION', name: '掐丝点蓝制作费', amount: 50, status: 'ACTIVE' },
        { code: 'ACCESSORY_PRODUCTION', name: '配饰制作费', amount: 30, status: 'ACTIVE' }
      ],

      // 填报规则数据
      reimbursementTypes: [
        { code: 'TRAVEL', name: '交通费', limit: 1000, status: 'ACTIVE' },
        { code: 'MEAL', name: '餐费', limit: 500, status: 'ACTIVE' },
        { code: 'MATERIAL', name: '材料费', limit: 2000, status: 'ACTIVE' },
        { code: 'ACCOMMODATION', name: '住宿费', limit: 800, status: 'ACTIVE' },
        { code: 'OTHER', name: '其他', limit: 500, status: 'ACTIVE' }
      ],

      approvalProcess: {
        levels: 2
      },

      // 系统全局配置
      systemConfig: {
        defaultWorkDays: 22,
        calculationTimeout: 30,
        batchSize: 100,
        defaultDataLevel: 'PERSONAL',
        operationPermissions: ['CALCULATE_SALARY', 'APPROVE_SALARY']
      },

      // 弹窗状态
      itemTemplateModalVisible: false,
      productionRulesModalVisible: false,
      reimbursementTypeModalVisible: false,
      calculationEngineModalVisible: false,
      matchingLogicModalVisible: false,
      defaultSettingsModalVisible: false,
      batchConfigModalVisible: false,
      approvalProcessModalVisible: false
    }
  },

  mounted() {
    this.loadConfigStatus()
  },

  methods: {
    // 加载配置状态
    loadConfigStatus() {
      // TODO: 从后端加载各模块配置状态
      console.log('加载配置状态')
    },

    // 配置状态总览点击事件
    handlePersonalRulesConfig() {
      this.$message.info('跳转到个人薪酬规则配置区域')
      // 滚动到对应区域
      this.$nextTick(() => {
        const element = document.querySelector('.config-section-description')
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' })
        }
      })
    },

    handleSystemRulesConfig() {
      this.productionRulesModalVisible = true
    },

    handleManualRulesConfig() {
      this.$router.push('/salary/reimbursement')
    },

    // 个人薪酬规则配置功能
    handleSalaryItemTemplates() {
      this.itemTemplateModalVisible = true
    },

    handleDefaultSettings() {
      this.defaultSettingsModalVisible = true
    },

    handleBatchConfig() {
      this.batchConfigModalVisible = true
    },

    // 系统自动匹配规则配置功能
    handleProductionRules() {
      this.productionRulesModalVisible = true
    },

    handleMatchingLogic() {
      this.matchingLogicModalVisible = true
    },

    handleCalculationEngine() {
      this.calculationEngineModalVisible = true
    },

    // 填报规则管理功能
    handleReimbursementTypes() {
      this.reimbursementTypeModalVisible = true
    },

    handleApprovalProcess() {
      this.approvalProcessModalVisible = true
    },

    handleReimbursementManagement() {
      this.$router.push('/salary/reimbursement')
    },

    // 系统全局配置功能
    handleSaveGlobalConfig() {
      // TODO: 保存系统全局配置到后端
      this.$message.success('全局配置保存成功')
    },

    handleResetGlobalConfig() {
      this.systemConfig = {
        defaultWorkDays: 22,
        calculationTimeout: 30,
        batchSize: 100,
        defaultDataLevel: 'PERSONAL',
        operationPermissions: ['CALCULATE_SALARY', 'APPROVE_SALARY']
      }
      this.$message.info('全局配置已重置')
    },

    // 弹窗处理方法
    handleItemTemplateModalOk(templateData) {
      this.itemTemplateModalVisible = false
      this.$message.success('薪酬项目模板保存成功')
      // TODO: 保存模板数据到后端
    },

    handleItemTemplateModalCancel() {
      this.itemTemplateModalVisible = false
    },

    handleProductionRulesModalOk(rulesData) {
      this.productionRulesModalVisible = false
      this.$message.success('生产提成规则保存成功')
      // TODO: 保存规则数据到后端
    },

    handleProductionRulesModalCancel() {
      this.productionRulesModalVisible = false
    },

    handleReimbursementTypeModalOk(typeData) {
      this.reimbursementTypeModalVisible = false
      this.$message.success('报销类型配置保存成功')
      // TODO: 保存类型数据到后端
    },

    handleReimbursementTypeModalCancel() {
      this.reimbursementTypeModalVisible = false
    },

    // 计算引擎参数配置弹窗处理
    handleCalculationEngineModalOk(engineData) {
      this.calculationEngineModalVisible = false
      this.$message.success('计算引擎参数配置保存成功')
      // TODO: 保存引擎参数到后端
    },

    handleCalculationEngineModalCancel() {
      this.calculationEngineModalVisible = false
    },

    // 匹配逻辑设置弹窗处理
    handleMatchingLogicModalOk(logicData) {
      this.matchingLogicModalVisible = false
      this.$message.success('数据匹配逻辑设置保存成功')
      // TODO: 保存匹配逻辑到后端
    },

    handleMatchingLogicModalCancel() {
      this.matchingLogicModalVisible = false
    },

    // 默认值设置弹窗处理
    handleDefaultSettingsModalOk(settingsData) {
      this.defaultSettingsModalVisible = false
      this.$message.success('默认值设置保存成功')
      // TODO: 保存默认设置到后端
    },

    handleDefaultSettingsModalCancel() {
      this.defaultSettingsModalVisible = false
    },

    // 批量配置工具弹窗处理
    handleBatchConfigModalOk(batchData) {
      this.batchConfigModalVisible = false
      this.$message.success('批量配置执行成功')
      // TODO: 执行批量配置操作
    },

    handleBatchConfigModalCancel() {
      this.batchConfigModalVisible = false
    },

    // 审批流程配置弹窗处理
    handleApprovalProcessModalOk(processData) {
      this.approvalProcessModalVisible = false
      this.$message.success('审批流程配置保存成功')
      // TODO: 保存审批流程配置到后端
    },

    handleApprovalProcessModalCancel() {
      this.approvalProcessModalVisible = false
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';

.config-header {
  text-align: center;
  margin-bottom: 32px;
}

.config-header h2 {
  color: #1890ff;
  margin-bottom: 8px;
}

.config-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 0;
}

.status-card {
  text-align: center;
  transition: all 0.3s;
}

.status-card:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.status-actions {
  margin-top: 12px;
}

.config-section-description {
  margin-bottom: 16px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 140px;
}

.function-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.function-card-content {
  text-align: center;
  padding: 16px;
}

.function-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 12px;
}

.function-card-content h4 {
  margin-bottom: 8px;
  color: #333;
}

.function-card-content p {
  color: #666;
  font-size: 12px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.function-status {
  margin-top: 8px;
}
</style>
