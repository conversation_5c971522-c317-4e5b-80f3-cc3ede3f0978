<template>
  <div class="payroll-elements">
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="项目名称">
                <a-input v-model="queryParam.elementName" placeholder="请输入项目名称" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="项目类型">
                <a-select v-model="queryParam.elementType" placeholder="请选择类型" allowClear>
                  <a-select-option value="INCOME">收入项</a-select-option>
                  <a-select-option value="DEDUCTION">扣除项</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="状态">
                <a-select v-model="queryParam.status" placeholder="请选择状态" allowClear>
                  <a-select-option value="0">正常</a-select-option>
                  <a-select-option value="1">停用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" style="text-align: right;">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div class="table-operator">
        <a-button type="primary" icon="plus" @click="handleAdd">新增薪酬项目</a-button>
        <a-button type="danger" icon="delete" :disabled="selectedRowKeys.length === 0" @click="handleBatchDelete">批量删除</a-button>
      </div>

      <a-table
        ref="table"
        size="default"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="elementType" slot-scope="text">
          <a-tag :color="text === 'INCOME' ? 'green' : 'orange'">
            {{ text === 'INCOME' ? '收入项' : '扣除项' }}
          </a-tag>
        </template>
        
        <template slot="calculationRule" slot-scope="text">
          <a-tag color="blue">
            {{ getCalculationRuleText(text) }}
          </a-tag>
        </template>
        
        <template slot="status" slot-scope="text">
          <a-tag :color="text === '0' ? 'green' : 'red'">
            {{ text === '0' ? '正常' : '停用' }}
          </a-tag>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleView(record)">查看</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
            <a style="color: red;">删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑模态框 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirmLoading="confirmLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="项目编码">
          <a-input v-decorator="['elementCode', { rules: [{ required: true, message: '请输入项目编码' }] }]" placeholder="请输入项目编码" />
        </a-form-item>
        
        <a-form-item label="项目名称">
          <a-input v-decorator="['elementName', { rules: [{ required: true, message: '请输入项目名称' }] }]" placeholder="请输入项目名称" />
        </a-form-item>
        
        <a-form-item label="项目类型">
          <a-select v-decorator="['elementType', { rules: [{ required: true, message: '请选择项目类型' }] }]" placeholder="请选择项目类型">
            <a-select-option value="INCOME">收入项</a-select-option>
            <a-select-option value="DEDUCTION">扣除项</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="计算规则">
          <a-select v-decorator="['calculationRule', { rules: [{ required: true, message: '请选择计算规则' }] }]" placeholder="请选择计算规则">
            <a-select-option value="FIXED">固定金额</a-select-option>
            <a-select-option value="FORMULA">公式计算</a-select-option>
            <a-select-option value="HOURLY">按小时计算</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="计算公式">
          <a-textarea v-decorator="['formulaExpression']" placeholder="请输入计算公式说明" :rows="3" />
        </a-form-item>
        
        <a-form-item label="税务属性">
          <a-select v-decorator="['taxAttribute', { initialValue: 'TAXABLE' }]" placeholder="请选择税务属性">
            <a-select-option value="TAXABLE">应税</a-select-option>
            <a-select-option value="NON_TAXABLE">免税</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="显示顺序">
          <a-input-number v-decorator="['displayOrder', { initialValue: 0 }]" :min="0" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="状态">
          <a-radio-group v-decorator="['status', { initialValue: '0' }]">
            <a-radio value="0">正常</a-radio>
            <a-radio value="1">停用</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea v-decorator="['remark']" placeholder="请输入备注" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看详情模态框 -->
    <a-modal
      title="薪酬项目详情"
      :visible="viewModalVisible"
      @cancel="viewModalVisible = false"
      :footer="null"
      width="600px"
    >
      <a-descriptions :column="2" v-if="currentRecord">
        <a-descriptions-item label="项目编码">{{ currentRecord.elementCode }}</a-descriptions-item>
        <a-descriptions-item label="项目名称">{{ currentRecord.elementName }}</a-descriptions-item>
        <a-descriptions-item label="项目类型">
          <a-tag :color="currentRecord.elementType === 'INCOME' ? 'green' : 'orange'">
            {{ currentRecord.elementType === 'INCOME' ? '收入项' : '扣除项' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="计算规则">
          <a-tag color="blue">{{ getCalculationRuleText(currentRecord.calculationRule) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="税务属性">{{ currentRecord.taxAttribute === 'TAXABLE' ? '应税' : '免税' }}</a-descriptions-item>
        <a-descriptions-item label="显示顺序">{{ currentRecord.displayOrder }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentRecord.status === '0' ? 'green' : 'red'">
            {{ currentRecord.status === '0' ? '正常' : '停用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentRecord.createTime }}</a-descriptions-item>
        <a-descriptions-item label="计算公式" :span="2">{{ currentRecord.formulaExpression || '无' }}</a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">{{ currentRecord.remark || '无' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'PayrollElements',
  data() {
    return {
      // 查询参数
      queryParam: {
        elementName: '',
        elementType: '',
        status: ''
      },
      // 表格数据
      dataSource: [],
      selectedRowKeys: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条记录`
      },
      // 模态框
      modalVisible: false,
      viewModalVisible: false,
      confirmLoading: false,
      modalTitle: '',
      currentRecord: null,
      form: this.$form.createForm(this),
      // 表格列定义
      columns: [
        {
          title: '项目编码',
          dataIndex: 'elementCode',
          key: 'elementCode',
          width: 120
        },
        {
          title: '项目名称',
          dataIndex: 'elementName',
          key: 'elementName',
          width: 150
        },
        {
          title: '项目类型',
          dataIndex: 'elementType',
          key: 'elementType',
          width: 100,
          scopedSlots: { customRender: 'elementType' }
        },
        {
          title: '计算规则',
          dataIndex: 'calculationRule',
          key: 'calculationRule',
          width: 120,
          scopedSlots: { customRender: 'calculationRule' }
        },
        {
          title: '计算公式',
          dataIndex: 'formulaExpression',
          key: 'formulaExpression',
          width: 200,
          ellipsis: true
        },
        {
          title: '显示顺序',
          dataIndex: 'displayOrder',
          key: 'displayOrder',
          width: 100
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true
      // 模拟数据 - 实际应该调用API
      setTimeout(() => {
        this.dataSource = [
          {
            id: 1,
            elementCode: 'DAILY_WAGE',
            elementName: '日薪',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            formulaExpression: '日薪 × 工作天数',
            taxAttribute: 'TAXABLE',
            displayOrder: 11,
            status: '0',
            createTime: '2024-12-23 10:00:00'
          },
          {
            id: 2,
            elementCode: 'SALES_COMMISSION_RATE',
            elementName: '销售提成',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            formulaExpression: '销售额 × 提成比例',
            taxAttribute: 'TAXABLE',
            displayOrder: 12,
            status: '0',
            createTime: '2024-12-23 10:00:00'
          },
          {
            id: 3,
            elementCode: 'CLOISONNE_PRODUCTION_FEE',
            elementName: '掐丝点蓝制作费',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            formulaExpression: '掐丝点蓝作品数量 × 单价',
            taxAttribute: 'TAXABLE',
            displayOrder: 18,
            status: '0',
            createTime: '2024-12-23 10:00:00'
          }
        ]
        this.pagination.total = this.dataSource.length
        this.loading = false
      }, 1000)
    },
    
    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    
    // 重置
    handleReset() {
      this.queryParam = {
        elementName: '',
        elementType: '',
        status: ''
      }
      this.handleSearch()
    },
    
    // 新增
    handleAdd() {
      this.modalTitle = '新增薪酬项目'
      this.modalVisible = true
      this.currentRecord = null
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },
    
    // 编辑
    handleEdit(record) {
      this.modalTitle = '编辑薪酬项目'
      this.modalVisible = true
      this.currentRecord = record
      this.$nextTick(() => {
        this.form.setFieldsValue({
          elementCode: record.elementCode,
          elementName: record.elementName,
          elementType: record.elementType,
          calculationRule: record.calculationRule,
          formulaExpression: record.formulaExpression,
          taxAttribute: record.taxAttribute,
          displayOrder: record.displayOrder,
          status: record.status,
          remark: record.remark
        })
      })
    },
    
    // 查看详情
    handleView(record) {
      this.currentRecord = record
      this.viewModalVisible = true
    },
    
    // 删除
    handleDelete(id) {
      this.$message.success('删除成功')
      this.loadData()
    },
    
    // 批量删除
    handleBatchDelete() {
      this.$message.success(`批量删除 ${this.selectedRowKeys.length} 条记录成功`)
      this.selectedRowKeys = []
      this.loadData()
    },
    
    // 提交表单
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          setTimeout(() => {
            this.$message.success(this.currentRecord ? '编辑成功' : '新增成功')
            this.modalVisible = false
            this.confirmLoading = false
            this.loadData()
          }, 1000)
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.modalVisible = false
      this.form.resetFields()
    },
    
    // 表格变化
    handleTableChange(pagination) {
      this.pagination = pagination
      this.loadData()
    },
    
    // 选择变化
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    
    // 获取计算规则文本
    getCalculationRuleText(rule) {
      const rules = {
        'FIXED': '固定金额',
        'FORMULA': '公式计算',
        'HOURLY': '按小时计算'
      }
      return rules[rule] || rule
    }
  }
}
</script>

<style scoped>
.payroll-elements {
  padding: 24px;
}

.table-page-search-wrapper {
  margin-bottom: 16px;
}

.table-operator {
  margin-bottom: 16px;
}

.table-operator .ant-btn {
  margin-right: 8px;
}
</style>
