<template>
  <div class="salary-management">
    <a-card title="薪酬管理系统" :bordered="false">
      <div class="content">
        <a-row :gutter="24">
          <a-col :span="24">
            <div class="welcome-section">
              <h2>🎉 欢迎使用珐琅馆薪酬管理系统</h2>
              <p>专为珐琅馆定制的智能薪酬管理解决方案</p>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="8">
            <a-card title="📊 数据统计" size="small">
              <p><strong>薪酬项目：</strong>{{ statistics.totalElements }}个</p>
              <p><strong>员工档案：</strong>{{ statistics.totalEmployees }}个</p>
              <p><strong>工资单：</strong>{{ statistics.totalPayslips }}个</p>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card title="🎨 个性化功能" size="small">
              <ul>
                <li>日薪制度</li>
                <li>多种销售提成</li>
                <li>制作费计算</li>
                <li>教学费管理</li>
              </ul>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card title="🔧 系统状态" size="small">
              <p><strong>后端服务：</strong><a-tag color="green">正常</a-tag></p>
              <p><strong>数据库：</strong><a-tag color="green">连接正常</a-tag></p>
              <p><strong>计算引擎：</strong><a-tag color="green">就绪</a-tag></p>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="24">
            <a-card title="🚀 快速操作" size="small">
              <a-space size="large">
                <a-button type="primary" @click="testPayslipGeneration">
                  <a-icon type="calculator" />
                  测试工资单生成
                </a-button>
                <a-button @click="viewPayrollElements">
                  <a-icon type="unordered-list" />
                  查看薪酬项目
                </a-button>
                <a-button @click="viewEmployeeStructure">
                  <a-icon type="team" />
                  员工薪资结构
                </a-button>
                <a-button @click="viewPayslips">
                  <a-icon type="file-text" />
                  工资单查询
                </a-button>
              </a-space>
            </a-card>
          </a-col>
        </a-row>

        <a-row :gutter="24" style="margin-top: 24px;">
          <a-col :span="24">
            <a-card title="📋 最近工资单" size="small">
              <a-table 
                :columns="payslipColumns" 
                :dataSource="recentPayslips" 
                :pagination="false"
                size="small"
              >
                <template slot="status" slot-scope="text">
                  <a-tag :color="getStatusColor(text)">{{ getStatusText(text) }}</a-tag>
                </template>
                <template slot="action" slot-scope="text, record">
                  <a @click="viewPayslipDetail(record)">查看详情</a>
                </template>
              </a-table>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 工资单详情模态框 -->
    <a-modal
      title="工资单详情"
      :visible="detailModalVisible"
      @cancel="detailModalVisible = false"
      :footer="null"
      width="800px"
    >
      <div v-if="selectedPayslip">
        <a-descriptions title="基本信息" :column="2">
          <a-descriptions-item label="员工姓名">{{ selectedPayslip.employeeName }}</a-descriptions-item>
          <a-descriptions-item label="薪酬期间">{{ selectedPayslip.payPeriod }}</a-descriptions-item>
          <a-descriptions-item label="应发工资">¥{{ selectedPayslip.grossSalary }}</a-descriptions-item>
          <a-descriptions-item label="实发工资">¥{{ selectedPayslip.netSalary }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>薪酬明细</h4>
        <a-table 
          :columns="detailColumns" 
          :dataSource="payslipDetails" 
          :pagination="false"
          size="small"
        />
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'SalaryManagement',
  data() {
    return {
      statistics: {
        totalElements: 23,
        totalEmployees: 5,
        totalPayslips: 1
      },
      detailModalVisible: false,
      selectedPayslip: null,
      payslipDetails: [],
      payslipColumns: [
        {
          title: '工资单号',
          dataIndex: 'payslipNumber',
          key: 'payslipNumber'
        },
        {
          title: '员工姓名',
          dataIndex: 'employeeName',
          key: 'employeeName'
        },
        {
          title: '薪酬期间',
          dataIndex: 'payPeriod',
          key: 'payPeriod'
        },
        {
          title: '实发工资',
          dataIndex: 'netSalary',
          key: 'netSalary',
          render: (text) => `¥${text}`
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      detailColumns: [
        {
          title: '薪酬项目',
          dataIndex: 'elementName',
          key: 'elementName'
        },
        {
          title: '计算公式',
          dataIndex: 'calculationFormula',
          key: 'calculationFormula'
        },
        {
          title: '金额',
          dataIndex: 'calculatedAmount',
          key: 'calculatedAmount',
          render: (text) => `¥${text}`
        }
      ],
      recentPayslips: [
        {
          key: '1',
          payslipNumber: 'PAY202412147',
          employeeName: '龚锦华',
          payPeriod: '2024-12',
          grossSalary: '4,600.00',
          netSalary: '4,600.00',
          status: '0'
        }
      ]
    }
  },
  methods: {
    testPayslipGeneration() {
      this.$message.info('正在测试工资单生成功能...')
      // 这里可以调用后端API
      setTimeout(() => {
        this.$message.success('测试完成！龚锦华12月工资单：¥4,600.00')
      }, 2000)
    },
    viewPayrollElements() {
      this.$message.info('薪酬项目管理功能开发中...')
    },
    viewEmployeeStructure() {
      this.$message.info('员工薪资结构管理功能开发中...')
    },
    viewPayslips() {
      this.$message.info('工资单查询功能开发中...')
    },
    viewPayslipDetail(record) {
      this.selectedPayslip = record
      this.payslipDetails = [
        { key: '1', elementName: '日薪', calculationFormula: '350元 × 5天', calculatedAmount: '1,750.00' },
        { key: '2', elementName: '销售提成', calculationFormula: '10,000元 × 5%', calculatedAmount: '500.00' },
        { key: '3', elementName: '咖啡店提成', calculationFormula: '5,000元 × 3%', calculatedAmount: '150.00' },
        { key: '4', elementName: '掐丝点蓝制作费', calculationFormula: '3件 × 200元', calculatedAmount: '600.00' },
        { key: '5', elementName: '配饰制作费', calculationFormula: '5件 × 80元', calculatedAmount: '400.00' },
        { key: '6', elementName: '讲师费（在馆）', calculationFormula: '8课时 × 150元', calculatedAmount: '1,200.00' }
      ]
      this.detailModalVisible = true
    },
    getStatusColor(status) {
      const colors = {
        '0': 'orange',
        '1': 'blue',
        '2': 'green'
      }
      return colors[status] || 'default'
    },
    getStatusText(status) {
      const texts = {
        '0': '草稿',
        '1': '已确认',
        '2': '已发放'
      }
      return texts[status] || '未知'
    }
  }
}
</script>

<style scoped>
.salary-management {
  padding: 24px;
}

.welcome-section {
  text-align: center;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 24px;
}

.welcome-section h2 {
  color: white;
  margin-bottom: 8px;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
}

.ant-card {
  margin-bottom: 16px;
}

.ant-card-small > .ant-card-head {
  min-height: 48px;
  padding: 0 12px;
  font-size: 14px;
}

.ant-card-small > .ant-card-body {
  padding: 12px;
}
</style>
