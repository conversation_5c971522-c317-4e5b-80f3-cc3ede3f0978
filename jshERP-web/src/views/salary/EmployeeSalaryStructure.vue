<template>
  <div class="employee-salary-structure">
    <a-card :bordered="false">
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="48">
            <a-col :md="8" :sm="24">
              <a-form-item label="员工姓名">
                <a-input v-model="queryParam.employeeName" placeholder="请输入员工姓名" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-form-item label="薪酬项目">
                <a-select v-model="queryParam.elementName" placeholder="请选择薪酬项目" allowClear>
                  <a-select-option value="日薪">日薪</a-select-option>
                  <a-select-option value="销售提成">销售提成</a-select-option>
                  <a-select-option value="制作费">制作费</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="24">
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <div class="table-operator">
        <a-button type="primary" icon="plus" @click="handleAdd">配置员工薪酬</a-button>
        <a-button icon="copy" @click="handleCopyStructure">复制薪资结构</a-button>
        <a-button type="danger" icon="delete" :disabled="selectedRowKeys.length === 0" @click="handleBatchDelete">批量删除</a-button>
      </div>

      <a-table
        ref="table"
        size="default"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="elementType" slot-scope="text">
          <a-tag :color="text === 'INCOME' ? 'green' : 'orange'">
            {{ text === 'INCOME' ? '收入项' : '扣除项' }}
          </a-tag>
        </template>
        
        <template slot="amountOrRate" slot-scope="text, record">
          <span v-if="record.calculationRule === 'FORMULA'">
            {{ text }}%
          </span>
          <span v-else>
            ¥{{ text }}
          </span>
        </template>
        
        <template slot="status" slot-scope="text">
          <a-tag :color="text === '0' ? 'green' : 'red'">
            {{ text === '0' ? '正常' : '停用' }}
          </a-tag>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleView(record)">查看</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="handleDelete(record.id)">
            <a style="color: red;">删除</a>
          </a-popconfirm>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑模态框 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :confirmLoading="confirmLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="600px"
    >
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="员工">
          <a-select v-decorator="['employeeId', { rules: [{ required: true, message: '请选择员工' }] }]" placeholder="请选择员工" @change="handleEmployeeChange">
            <a-select-option value="147">龚锦华</a-select-option>
            <a-select-option value="149">伍尚明</a-select-option>
            <a-select-option value="150">张三</a-select-option>
            <a-select-option value="151">李四</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="薪酬项目">
          <a-select v-decorator="['elementId', { rules: [{ required: true, message: '请选择薪酬项目' }] }]" placeholder="请选择薪酬项目" @change="handleElementChange">
            <a-select-option value="1">日薪</a-select-option>
            <a-select-option value="2">销售提成</a-select-option>
            <a-select-option value="3">咖啡店提成</a-select-option>
            <a-select-option value="4">掐丝点蓝制作费</a-select-option>
            <a-select-option value="5">配饰制作费</a-select-option>
            <a-select-option value="6">讲师费（在馆）</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="金额/费率">
          <a-input-number 
            v-decorator="['amountOrRate', { rules: [{ required: true, message: '请输入金额或费率' }] }]" 
            :precision="selectedElementType === 'FORMULA' ? 4 : 2"
            :min="0"
            :max="selectedElementType === 'FORMULA' ? 1 : 999999"
            style="width: 100%" 
            placeholder="请输入金额或费率"
          />
          <div class="ant-form-explain" v-if="selectedElementType === 'FORMULA'">
            公式计算项目请输入比例（如：0.05 表示 5%）
          </div>
          <div class="ant-form-explain" v-else>
            固定金额项目请输入具体金额（如：350 表示 350元）
          </div>
        </a-form-item>
        
        <a-form-item label="生效日期">
          <a-date-picker v-decorator="['effectiveDate', { rules: [{ required: true, message: '请选择生效日期' }] }]" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="失效日期">
          <a-date-picker v-decorator="['expiryDate']" style="width: 100%" placeholder="不填表示长期有效" />
        </a-form-item>
        
        <a-form-item label="状态">
          <a-radio-group v-decorator="['status', { initialValue: '0' }]">
            <a-radio value="0">正常</a-radio>
            <a-radio value="1">停用</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="备注">
          <a-textarea v-decorator="['remark']" placeholder="请输入备注" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 复制薪资结构模态框 -->
    <a-modal
      title="复制薪资结构"
      :visible="copyModalVisible"
      @ok="handleCopySubmit"
      @cancel="copyModalVisible = false"
      width="500px"
    >
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="源员工">
          <a-select v-model="copyForm.sourceEmployeeId" placeholder="请选择源员工">
            <a-select-option value="147">龚锦华</a-select-option>
            <a-select-option value="149">伍尚明</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="目标员工">
          <a-select v-model="copyForm.targetEmployeeId" placeholder="请选择目标员工" mode="multiple">
            <a-select-option value="150">张三</a-select-option>
            <a-select-option value="151">李四</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="生效日期">
          <a-date-picker v-model="copyForm.effectiveDate" style="width: 100%" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'EmployeeSalaryStructure',
  data() {
    return {
      queryParam: {
        employeeName: '',
        elementName: ''
      },
      dataSource: [],
      selectedRowKeys: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showTotal: (total) => `共 ${total} 条记录`
      },
      modalVisible: false,
      copyModalVisible: false,
      confirmLoading: false,
      modalTitle: '',
      currentRecord: null,
      selectedElementType: '',
      form: this.$form.createForm(this),
      copyForm: {
        sourceEmployeeId: '',
        targetEmployeeId: [],
        effectiveDate: null
      },
      columns: [
        {
          title: '员工姓名',
          dataIndex: 'employeeName',
          key: 'employeeName',
          width: 120
        },
        {
          title: '薪酬项目',
          dataIndex: 'elementName',
          key: 'elementName',
          width: 150
        },
        {
          title: '项目类型',
          dataIndex: 'elementType',
          key: 'elementType',
          width: 100,
          scopedSlots: { customRender: 'elementType' }
        },
        {
          title: '金额/费率',
          dataIndex: 'amountOrRate',
          key: 'amountOrRate',
          width: 120,
          scopedSlots: { customRender: 'amountOrRate' }
        },
        {
          title: '生效日期',
          dataIndex: 'effectiveDate',
          key: 'effectiveDate',
          width: 120
        },
        {
          title: '失效日期',
          dataIndex: 'expiryDate',
          key: 'expiryDate',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 80,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.loading = true
      // 模拟龚锦华和伍尚明的薪资结构数据
      setTimeout(() => {
        this.dataSource = [
          {
            id: 1,
            employeeId: 147,
            employeeName: '龚锦华',
            elementId: 1,
            elementName: '日薪',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            amountOrRate: 350.00,
            effectiveDate: '2024-12-01',
            expiryDate: null,
            status: '0'
          },
          {
            id: 2,
            employeeId: 147,
            employeeName: '龚锦华',
            elementId: 2,
            elementName: '销售提成',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            amountOrRate: 0.0500,
            effectiveDate: '2024-12-01',
            expiryDate: null,
            status: '0'
          },
          {
            id: 3,
            employeeId: 147,
            employeeName: '龚锦华',
            elementId: 4,
            elementName: '掐丝点蓝制作费',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            amountOrRate: 200.00,
            effectiveDate: '2024-12-01',
            expiryDate: null,
            status: '0'
          },
          {
            id: 4,
            employeeId: 149,
            employeeName: '伍尚明',
            elementId: 1,
            elementName: '日薪',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            amountOrRate: 280.00,
            effectiveDate: '2024-12-01',
            expiryDate: null,
            status: '0'
          },
          {
            id: 5,
            employeeId: 149,
            employeeName: '伍尚明',
            elementId: 4,
            elementName: '掐丝点蓝制作费',
            elementType: 'INCOME',
            calculationRule: 'FORMULA',
            amountOrRate: 180.00,
            effectiveDate: '2024-12-01',
            expiryDate: null,
            status: '0'
          }
        ]
        this.pagination.total = this.dataSource.length
        this.loading = false
      }, 1000)
    },
    
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },
    
    handleReset() {
      this.queryParam = {
        employeeName: '',
        elementName: ''
      }
      this.handleSearch()
    },
    
    handleAdd() {
      this.modalTitle = '配置员工薪酬'
      this.modalVisible = true
      this.currentRecord = null
      this.selectedElementType = ''
      this.$nextTick(() => {
        this.form.resetFields()
      })
    },
    
    handleEdit(record) {
      this.modalTitle = '编辑员工薪酬'
      this.modalVisible = true
      this.currentRecord = record
      this.selectedElementType = record.calculationRule
      this.$nextTick(() => {
        this.form.setFieldsValue({
          employeeId: record.employeeId,
          elementId: record.elementId,
          amountOrRate: record.amountOrRate,
          effectiveDate: record.effectiveDate ? this.$moment(record.effectiveDate) : null,
          expiryDate: record.expiryDate ? this.$moment(record.expiryDate) : null,
          status: record.status,
          remark: record.remark
        })
      })
    },
    
    handleView(record) {
      this.$message.info(`查看 ${record.employeeName} 的 ${record.elementName} 配置`)
    },
    
    handleDelete(id) {
      this.$message.success('删除成功')
      this.loadData()
    },
    
    handleBatchDelete() {
      this.$message.success(`批量删除 ${this.selectedRowKeys.length} 条记录成功`)
      this.selectedRowKeys = []
      this.loadData()
    },
    
    handleCopyStructure() {
      this.copyModalVisible = true
      this.copyForm = {
        sourceEmployeeId: '',
        targetEmployeeId: [],
        effectiveDate: null
      }
    },
    
    handleCopySubmit() {
      if (!this.copyForm.sourceEmployeeId || this.copyForm.targetEmployeeId.length === 0) {
        this.$message.error('请选择源员工和目标员工')
        return
      }
      this.$message.success(`成功复制薪资结构到 ${this.copyForm.targetEmployeeId.length} 个员工`)
      this.copyModalVisible = false
      this.loadData()
    },
    
    handleEmployeeChange(value) {
      // 员工变化时的处理
    },
    
    handleElementChange(value) {
      // 薪酬项目变化时更新计算规则类型
      const elements = {
        '1': 'FORMULA',
        '2': 'FORMULA', 
        '3': 'FORMULA',
        '4': 'FORMULA',
        '5': 'FORMULA',
        '6': 'FIXED'
      }
      this.selectedElementType = elements[value] || ''
    },
    
    handleSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          setTimeout(() => {
            this.$message.success(this.currentRecord ? '编辑成功' : '配置成功')
            this.modalVisible = false
            this.confirmLoading = false
            this.loadData()
          }, 1000)
        }
      })
    },
    
    handleCancel() {
      this.modalVisible = false
      this.form.resetFields()
    },
    
    handleTableChange(pagination) {
      this.pagination = pagination
      this.loadData()
    },
    
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    }
  }
}
</script>

<style scoped>
.employee-salary-structure {
  padding: 24px;
}

.table-page-search-wrapper {
  margin-bottom: 16px;
}

.table-operator {
  margin-bottom: 16px;
}

.table-operator .ant-btn {
  margin-right: 8px;
}
</style>
