<template>
  <a-modal
    title="报销申请审批"
    :visible="visible"
    :width="700"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="approval-content">
      <!-- 申请信息展示 -->
      <a-descriptions title="申请信息" :column="2" bordered>
        <a-descriptions-item label="申请单号">
          {{ record.applicationNumber }}
        </a-descriptions-item>
        <a-descriptions-item label="申请人">
          {{ record.applicantName }}
        </a-descriptions-item>
        <a-descriptions-item label="申请日期">
          {{ record.applicationDate }}
        </a-descriptions-item>
        <a-descriptions-item label="报销类型">
          <a-tag :color="getTypeColor(record.reimbursementType)">
            {{ getTypeText(record.reimbursementType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="报销金额">
          <span style="font-weight: bold; color: #1890ff;">¥{{ record.amount || 0 }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="费用发生日期">
          {{ record.expenseDate }}
        </a-descriptions-item>
        <a-descriptions-item label="费用说明" :span="2">
          {{ record.expenseDescription }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2" v-if="record.remark">
          {{ record.remark }}
        </a-descriptions-item>
      </a-descriptions>

      <!-- 发票凭证 -->
      <div class="invoice-section" v-if="record.invoiceFiles && record.invoiceFiles.length > 0">
        <h4>发票凭证</h4>
        <div class="invoice-list">
          <div 
            v-for="(file, index) in record.invoiceFiles" 
            :key="index"
            class="invoice-item">
            <a-icon type="file" />
            <span>{{ file.name }}</span>
            <a-button size="small" type="link" @click="previewFile(file)">预览</a-button>
          </div>
        </div>
      </div>

      <!-- 审批表单 -->
      <a-divider>审批意见</a-divider>
      
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-model-item label="审批结果" prop="approvalStatus">
          <a-radio-group v-model="form.approvalStatus">
            <a-radio value="APPROVED">通过</a-radio>
            <a-radio value="REJECTED">拒绝</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="审批金额" prop="approvedAmount" v-if="form.approvalStatus === 'APPROVED'">
          <a-input-number
            v-model="form.approvedAmount"
            :min="0"
            :max="record.amount"
            :precision="2"
            placeholder="请输入审批金额"
            style="width: 200px"
          />
          <span class="input-suffix">元（最大金额：¥{{ record.amount }}）</span>
        </a-form-model-item>

        <a-form-model-item label="审批意见" prop="approvalRemark">
          <a-textarea
            v-model="form.approvalRemark"
            :rows="3"
            placeholder="请输入审批意见"
          />
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
import { approveReimbursement } from '@/api/salary'

export default {
  name: 'ApprovalModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      confirmLoading: false,
      form: {
        approvalStatus: 'APPROVED',
        approvedAmount: 0,
        approvalRemark: ''
      },
      rules: {
        approvalStatus: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        approvedAmount: [
          { required: true, message: '请输入审批金额', trigger: 'blur' }
        ],
        approvalRemark: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
      }
    },
    'form.approvalStatus'(val) {
      if (val === 'APPROVED') {
        this.form.approvedAmount = this.record.amount
      } else {
        this.form.approvedAmount = 0
      }
    }
  },
  methods: {
    // 获取报销类型颜色
    getTypeColor(type) {
      const colorMap = {
        'TRAVEL': 'blue',
        'MEAL': 'green',
        'MATERIAL': 'orange',
        'ACCOMMODATION': 'purple',
        'OTHER': 'default'
      }
      return colorMap[type] || 'default'
    },
    
    // 获取报销类型文本
    getTypeText(type) {
      const textMap = {
        'TRAVEL': '交通费',
        'MEAL': '餐费',
        'MATERIAL': '材料费',
        'ACCOMMODATION': '住宿费',
        'OTHER': '其他'
      }
      return textMap[type] || type
    },

    // 预览文件
    previewFile(file) {
      // TODO: 实现文件预览功能
      this.$message.info('文件预览功能开发中...')
    },

    initForm() {
      this.form = {
        approvalStatus: 'APPROVED',
        approvedAmount: this.record.amount || 0,
        approvalRemark: ''
      }
    },

    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true

          const approvalData = {
            id: this.record.id,
            ...this.form,
            approvalDate: new Date().toISOString().split('T')[0]
          }

          approveReimbursement(approvalData).then(res => {
            if (res.success) {
              this.$emit('ok', approvalData)
              this.$message.success('审批成功')
            } else {
              this.$message.error(res.message || '审批失败')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.approval-content {
  max-height: 600px;
  overflow-y: auto;
}

.invoice-section {
  margin: 16px 0;
}

.invoice-section h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.invoice-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.invoice-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.ant-form-item {
  margin-bottom: 16px;
}
</style>
