<template>
  <a-modal
    title="薪资配置设置"
    :visible="visible"
    :width="900"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <a-tabs v-model="activeTab" type="card">
      <!-- 固定薪酬配置 -->
      <a-tab-pane key="fixed" tab="固定薪酬">
        <a-form-model
          ref="fixedForm"
          :model="fixedConfig"
          :rules="fixedRules"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-alert
            message="固定薪酬配置"
            description="配置员工的基础日薪标准和相关固定收入项目"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />

          <a-form-model-item label="标准日薪" prop="standardDailyWage">
            <a-input-number
              v-model="fixedConfig.standardDailyWage"
              :min="0"
              :precision="2"
              placeholder="请输入标准日薪"
              style="width: 300px"
            />
            <span class="input-suffix">元/天</span>
          </a-form-model-item>
        </a-form-model>
      </a-tab-pane>
      
      <!-- 销售提成配置 -->
      <a-tab-pane key="sales" tab="销售提成">
        <a-form-model
          ref="salesForm"
          :model="salesConfig"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-alert
            message="销售提成配置"
            description="配置各种销售场景的提成比例和计算规则"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />

          <!-- 咖啡店值班销售提成 -->
          <a-divider orientation="left">咖啡店值班销售</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-model-item label="销售额提成比例">
                <a-input-number
                  v-model="salesConfig.coffeeSalesRate"
                  :min="0"
                  :max="1"
                  :precision="3"
                  placeholder="请输入提成比例"
                  style="width: 100%"
                />
                <span class="input-suffix">按当天销售额</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="最低销售额要求">
                <a-input-number
                  v-model="salesConfig.coffeeMinSales"
                  :min="0"
                  :precision="2"
                  placeholder="请输入最低销售额"
                  style="width: 100%"
                />
                <span class="input-suffix">元</span>
              </a-form-model-item>
            </a-col>
          </a-row>

          <!-- 馆内销售提成 -->
          <a-divider orientation="left">馆内销售提成</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-model-item label="馆内销售提成比例">
                <a-input-number
                  v-model="salesConfig.hallSalesRate"
                  :min="0"
                  :max="1"
                  :precision="3"
                  placeholder="请输入提成比例"
                  style="width: 100%"
                />
                <span class="input-suffix">按销售金额</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="艺术作品销售提成">
                <a-input-number
                  v-model="salesConfig.artworkSalesRate"
                  :min="0"
                  :max="1"
                  :precision="3"
                  placeholder="请输入提成比例"
                  style="width: 100%"
                />
                <span class="input-suffix">按作品价值</span>
              </a-form-model-item>
            </a-col>
          </a-row>

          <!-- 渠道开发提成 -->
          <a-divider orientation="left">渠道开发提成</a-divider>
          <a-form-model-item label="渠道开发提成比例">
            <a-input-number
              v-model="salesConfig.channelDevelopmentRate"
              :min="0"
              :max="1"
              :precision="3"
              placeholder="请输入渠道开发提成比例"
              style="width: 100%"
            />
            <span class="input-suffix">按开发收入</span>
          </a-form-model-item>
        </a-form-model>
      </a-tab-pane>
      
      <!-- 项目收入配置 -->
      <a-tab-pane key="project" tab="项目收入">
        <a-form-model
          ref="projectForm"
          :model="projectConfig"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-alert
            message="项目收入配置"
            description="配置讲师费、助理费等项目收入的计算标准"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />

          <!-- 讲师费配置 -->
          <a-divider orientation="left">讲师费标准</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-model-item label="外出讲师费">
                <a-input-number
                  v-model="projectConfig.externalInstructorFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入外出讲师费"
                  style="width: 100%"
                />
                <span class="input-suffix">元/次</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="在馆讲师费">
                <a-input-number
                  v-model="projectConfig.internalInstructorFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入在馆讲师费"
                  style="width: 100%"
                />
                <span class="input-suffix">元/次</span>
              </a-form-model-item>
            </a-col>
          </a-row>

          <!-- 助理费配置 -->
          <a-divider orientation="left">助理费标准</a-divider>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-model-item label="外出助理费">
                <a-input-number
                  v-model="projectConfig.externalAssistantFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入外出助理费"
                  style="width: 100%"
                />
                <span class="input-suffix">元/次</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="在馆助理费">
                <a-input-number
                  v-model="projectConfig.internalAssistantFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入在馆助理费"
                  style="width: 100%"
                />
                <span class="input-suffix">元/次</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-tab-pane>

      <!-- 生产提成配置 -->
      <a-tab-pane key="production" tab="生产提成">
        <a-form-model
          ref="productionForm"
          :model="productionConfig"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-alert
            message="生产提成配置"
            description="配置掐丝点蓝制作费、配饰制作费等生产提成标准"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-model-item label="掐丝点蓝制作费">
                <a-input-number
                  v-model="productionConfig.cloisonneProductionFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入制作费标准"
                  style="width: 100%"
                />
                <span class="input-suffix">元/件</span>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="配饰制作费">
                <a-input-number
                  v-model="productionConfig.accessoryProductionFee"
                  :min="0"
                  :precision="2"
                  placeholder="请输入配饰制作费"
                  style="width: 100%"
                />
                <span class="input-suffix">元/件</span>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </a-tab-pane>

      <!-- 报销配置 -->
      <a-tab-pane key="reimbursement" tab="报销配置">
        <a-form-model
          ref="reimbursementForm"
          :model="reimbursementConfig"
          :label-col="{ span: 8 }"
          :wrapper-col="{ span: 14 }"
        >
          <a-alert
            message="报销配置"
            description="配置报销金额，按每次填报计算"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />

          <a-form-model-item label="报销金额">
            <a-input-number
              v-model="reimbursementConfig.amount"
              :min="0"
              :precision="2"
              placeholder="请输入报销金额"
              style="width: 300px"
            />
            <span class="input-suffix">元（按每次填报）</span>
          </a-form-model-item>
        </a-form-model>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script>
export default {
  name: 'SalaryConfigModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      activeTab: 'fixed',
      // 固定薪酬配置
      fixedConfig: {
        standardDailyWage: 200
      },
      // 销售提成配置
      salesConfig: {
        coffeeSalesRate: 0.05,
        coffeeMinSales: 500,
        hallSalesRate: 0.03,
        artworkSalesRate: 0.10,
        channelDevelopmentRate: 0.08
      },
      // 项目收入配置
      projectConfig: {
        externalInstructorFee: 800,
        internalInstructorFee: 500,
        externalAssistantFee: 400,
        internalAssistantFee: 300
      },
      // 生产提成配置
      productionConfig: {
        cloisonneProductionFee: 50,
        accessoryProductionFee: 30
      },
      // 报销配置
      reimbursementConfig: {
        amount: 0
      },
      // 验证规则
      fixedRules: {
        standardDailyWage: [
          { required: true, message: '请输入标准日薪', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOk() {
      // 验证所有表单
      const forms = ['fixedForm', 'salesForm', 'projectForm', 'productionForm', 'reimbursementForm']
      let allValid = true

      forms.forEach(formRef => {
        if (this.$refs[formRef]) {
          this.$refs[formRef].validate(valid => {
            if (!valid) {
              allValid = false
            }
          })
        }
      })

      if (allValid) {
        this.confirmLoading = true

        const configData = {
          fixed: this.fixedConfig,
          sales: this.salesConfig,
          project: this.projectConfig,
          production: this.productionConfig,
          reimbursement: this.reimbursementConfig
        }

        // 这里应该调用API保存配置
        setTimeout(() => {
          this.confirmLoading = false
          this.$emit('ok', configData)
          this.$message.success('薪酬配置保存成功')
        }, 1000)
      } else {
        this.$message.warning('请检查表单填写是否正确')
      }
    },

    handleCancel() {
      this.$emit('cancel')
    },

    // 重置配置为默认值
    resetToDefault() {
      this.$confirm({
        title: '确认重置',
        content: '确定要重置所有配置为默认值吗？',
        onOk: () => {
          this.fixedConfig = {
            standardDailyWage: 200
          }
          this.salesConfig = {
            coffeeSalesRate: 0.05,
            coffeeMinSales: 500,
            hallSalesRate: 0.03,
            artworkSalesRate: 0.10,
            channelDevelopmentRate: 0.08
          }
          this.projectConfig = {
            externalInstructorFee: 800,
            internalInstructorFee: 500,
            externalAssistantFee: 400,
            internalAssistantFee: 300
          }
          this.productionConfig = {
            cloisonneProductionFee: 50,
            accessoryProductionFee: 30
          }
          this.reimbursementConfig = {
            amount: 0
          }
          this.$message.success('已重置为默认配置')
        }
      })
    }
  }
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}

.ant-alert {
  border-radius: 6px;
}

.ant-tabs-card > .ant-tabs-content {
  margin-top: -16px;
}

.ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
}
</style>
