<template>
  <a-modal
    title="默认值设置"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="default-settings">
      <a-alert
        message="默认值设置"
        description="为新员工设置默认的薪酬项目配置，新员工入职时将自动应用这些默认设置。"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 默认启用的薪酬项目 -->
        <a-divider orientation="left">默认启用的薪酬项目</a-divider>

        <a-form-model-item label="默认薪酬项目" prop="defaultItems">
          <a-checkbox-group v-model="form.defaultItems">
            <a-row>
              <a-col :span="12" v-for="item in salaryItems" :key="item.code">
                <a-checkbox :value="item.code">{{ item.name }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
          <div class="setting-tip">选择新员工默认启用的薪酬项目</div>
        </a-form-model-item>

        <!-- 默认薪酬配置 -->
        <a-divider orientation="left">默认薪酬配置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="默认基础日薪" prop="defaultDailyWage">
              <a-input-number
                v-model="form.defaultDailyWage"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="新员工默认日薪"
              />
              <span class="input-suffix">元/天</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="默认咖啡店提成" prop="defaultCoffeeCommission">
              <a-input-number
                v-model="form.defaultCoffeeCommission"
                :min="0"
                :max="1"
                :precision="3"
                style="width: 100%"
                placeholder="默认提成比例"
              />
              <span class="input-suffix">%</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="默认外出讲师费" prop="defaultExternalInstructor">
              <a-input-number
                v-model="form.defaultExternalInstructor"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="默认外出讲师费"
              />
              <span class="input-suffix">元/次</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="默认在馆讲师费" prop="defaultInternalInstructor">
              <a-input-number
                v-model="form.defaultInternalInstructor"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="默认在馆讲师费"
              />
              <span class="input-suffix">元/次</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="默认外出助理费" prop="defaultExternalAssistant">
              <a-input-number
                v-model="form.defaultExternalAssistant"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="默认外出助理费"
              />
              <span class="input-suffix">元/次</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="默认在馆助理费" prop="defaultInternalAssistant">
              <a-input-number
                v-model="form.defaultInternalAssistant"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="默认在馆助理费"
              />
              <span class="input-suffix">元/次</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="默认渠道开发提成" prop="defaultChannelCommission">
              <a-input-number
                v-model="form.defaultChannelCommission"
                :min="0"
                :max="1"
                :precision="3"
                style="width: 100%"
                placeholder="默认渠道开发提成比例"
              />
              <span class="input-suffix">%</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="默认馆内销售提成" prop="defaultHallSalesCommission">
              <a-input-number
                v-model="form.defaultHallSalesCommission"
                :min="0"
                :max="1"
                :precision="3"
                style="width: 100%"
                placeholder="默认馆内销售提成比例"
              />
              <span class="input-suffix">%</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 默认部门配置 -->
        <a-divider orientation="left">按部门默认配置</a-divider>

        <a-form-model-item label="部门差异化配置" prop="departmentSpecific">
          <a-switch v-model="form.departmentSpecific" />
          <span style="margin-left: 8px;">启用按部门设置不同的默认值</span>
        </a-form-model-item>

        <div v-if="form.departmentSpecific">
          <a-table
            :columns="departmentColumns"
            :data-source="departmentDefaults"
            :pagination="false"
            size="small"
            bordered>
            
            <template slot="dailyWage" slot-scope="text, record">
              <a-input-number
                v-model="record.dailyWage"
                :min="0"
                :precision="2"
                size="small"
                style="width: 100%"
              />
            </template>
            
            <template slot="coffeeCommission" slot-scope="text, record">
              <a-input-number
                v-model="record.coffeeCommission"
                :min="0"
                :max="1"
                :precision="3"
                size="small"
                style="width: 100%"
              />
            </template>
          </a-table>
        </div>

        <!-- 自动应用设置 -->
        <a-divider orientation="left">自动应用设置</a-divider>

        <a-form-model-item label="自动应用默认配置" prop="autoApply">
          <a-switch v-model="form.autoApply" />
          <span style="margin-left: 8px;">新员工入职时自动应用默认配置</span>
        </a-form-model-item>

        <a-form-model-item label="覆盖现有配置" prop="overrideExisting">
          <a-switch v-model="form.overrideExisting" />
          <span style="margin-left: 8px;">应用默认配置时覆盖员工现有配置</span>
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'DefaultSettingsModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      
      // 可用的薪酬项目
      salaryItems: [
        { code: 'DAILY_WAGE', name: '基础日薪' },
        { code: 'COFFEE_COMMISSION', name: '咖啡店提成' },
        { code: 'EXTERNAL_INSTRUCTOR', name: '外出讲师费' },
        { code: 'INTERNAL_INSTRUCTOR', name: '在馆讲师费' },
        { code: 'EXTERNAL_ASSISTANT', name: '外出助理费' },
        { code: 'INTERNAL_ASSISTANT', name: '在馆助理费' },
        { code: 'CHANNEL_COMMISSION', name: '渠道开发提成' },
        { code: 'HALL_SALES_COMMISSION', name: '馆内销售提成' },
        { code: 'ARTWORK_SALES', name: '艺术作品销售' }
      ],
      
      // 表单数据
      form: {
        defaultItems: ['DAILY_WAGE'],
        defaultDailyWage: 200,
        defaultCoffeeCommission: 0.05,
        defaultExternalInstructor: 800,
        defaultInternalInstructor: 500,
        defaultExternalAssistant: 400,
        defaultInternalAssistant: 300,
        defaultChannelCommission: 0.08,
        defaultHallSalesCommission: 0.03,
        departmentSpecific: false,
        autoApply: true,
        overrideExisting: false
      },
      
      // 部门默认配置
      departmentDefaults: [
        { department: '珐琅制作', dailyWage: 250, coffeeCommission: 0 },
        { department: '咖啡服务', dailyWage: 180, coffeeCommission: 0.05 },
        { department: '培训教学', dailyWage: 300, coffeeCommission: 0 },
        { department: '业务拓展', dailyWage: 220, coffeeCommission: 0.03 }
      ],
      
      // 部门配置表格列
      departmentColumns: [
        { title: '部门', dataIndex: 'department', width: 120 },
        { title: '日薪(元)', dataIndex: 'dailyWage', width: 120, scopedSlots: { customRender: 'dailyWage' } },
        { title: '咖啡店提成(%)', dataIndex: 'coffeeCommission', width: 150, scopedSlots: { customRender: 'coffeeCommission' } }
      ],
      
      // 验证规则
      rules: {
        defaultItems: [{ required: true, message: '请选择默认薪酬项目', trigger: 'change' }],
        defaultDailyWage: [{ required: true, message: '请输入默认基础日薪', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          
          // 模拟API调用
          setTimeout(() => {
            this.confirmLoading = false
            this.$emit('ok', {
              ...this.form,
              departmentDefaults: this.form.departmentSpecific ? this.departmentDefaults : null
            })
            this.$message.success('默认值设置保存成功')
          }, 1000)
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.default-settings {
  max-height: 600px;
  overflow-y: auto;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.setting-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}
</style>
