<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <!-- 基本信息区域 -->
      <a-divider orientation="left">基本信息</a-divider>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="申请人" prop="applicantId">
            <a-select
              v-model="form.applicantId"
              placeholder="请选择申请人"
              showSearch
              optionFilterProp="children"
              @change="handleApplicantChange">
              <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
                {{ user.userName }} ({{ user.realName }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="申请日期" prop="applicationDate">
            <a-date-picker
              v-model="form.applicationDate"
              format="YYYY-MM-DD"
              placeholder="请选择申请日期"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="报销类型" prop="reimbursementType">
            <a-select v-model="form.reimbursementType" placeholder="请选择报销类型">
              <a-select-option value="TRAVEL">交通费</a-select-option>
              <a-select-option value="MEAL">餐费</a-select-option>
              <a-select-option value="MATERIAL">材料费</a-select-option>
              <a-select-option value="ACCOMMODATION">住宿费</a-select-option>
              <a-select-option value="OTHER">其他</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="报销金额" prop="amount">
            <a-input-number
              v-model="form.amount"
              :min="0"
              :precision="2"
              placeholder="请输入报销金额"
              style="width: 100%"
            />
            <span class="input-suffix">元</span>
          </a-form-model-item>
        </a-col>
      </a-row>

      <!-- 报销详情区域 -->
      <a-divider orientation="left">报销详情</a-divider>

      <a-form-model-item label="费用发生日期" prop="expenseDate">
        <a-date-picker
          v-model="form.expenseDate"
          format="YYYY-MM-DD"
          placeholder="请选择费用发生日期"
          style="width: 300px"
        />
      </a-form-model-item>

      <a-form-model-item label="费用说明" prop="expenseDescription">
        <a-textarea
          v-model="form.expenseDescription"
          :rows="3"
          placeholder="请详细说明费用用途和明细"
        />
      </a-form-model-item>

      <a-form-model-item label="发票凭证" prop="invoiceFiles">
        <a-upload
          v-model="form.invoiceFiles"
          name="file"
          :multiple="true"
          :file-list="fileList"
          @change="handleFileChange"
          :before-upload="beforeUpload"
        >
          <a-button>
            <a-icon type="upload" /> 上传发票
          </a-button>
        </a-upload>
        <div class="upload-tip">
          支持上传jpg、png、pdf格式文件，单个文件不超过5MB
        </div>
      </a-form-model-item>

      <a-form-model-item label="备注" prop="remark">
        <a-textarea
          v-model="form.remark"
          :rows="2"
          placeholder="请输入备注信息"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { getUserList } from '@/api/api'
import { addReimbursement, updateReimbursement } from '@/api/salary'

export default {
  name: 'ReimbursementModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      confirmLoading: false,
      userList: [],
      fileList: [],
      form: {
        id: null,
        applicantId: null,
        applicantName: '',
        applicationDate: null,
        reimbursementType: '',
        amount: 0,
        expenseDate: null,
        expenseDescription: '',
        invoiceFiles: [],
        remark: ''
      },
      rules: {
        applicantId: [
          { required: true, message: '请选择申请人', trigger: 'change' }
        ],
        applicationDate: [
          { required: true, message: '请选择申请日期', trigger: 'change' }
        ],
        reimbursementType: [
          { required: true, message: '请选择报销类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入报销金额', trigger: 'blur' }
        ],
        expenseDate: [
          { required: true, message: '请选择费用发生日期', trigger: 'change' }
        ],
        expenseDescription: [
          { required: true, message: '请输入费用说明', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    modalTitle() {
      return this.form.id ? '编辑报销申请' : '新增报销申请'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadUserList()
      }
    }
  },
  methods: {
    // 加载用户列表
    loadUserList() {
      getUserList().then(res => {
        if (res.success) {
          this.userList = res.result || []
        }
      })
    },

    // 申请人选择变化
    handleApplicantChange(applicantId) {
      const selectedUser = this.userList.find(user => user.id === applicantId)
      if (selectedUser) {
        this.form.applicantName = selectedUser.realName || selectedUser.userName
      }
    },

    // 文件上传变化
    handleFileChange(info) {
      this.fileList = [...info.fileList]
    },

    // 上传前检查
    beforeUpload(file) {
      const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、PDF 格式的文件!')
        return false
      }
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 5MB!')
        return false
      }
      return true
    },

    initForm() {
      if (this.record && this.record.id) {
        // 编辑模式
        this.form = { ...this.record }
        if (this.form.applicationDate) {
          this.form.applicationDate = this.$moment(this.form.applicationDate)
        }
        if (this.form.expenseDate) {
          this.form.expenseDate = this.$moment(this.form.expenseDate)
        }
        // 加载文件列表
        this.fileList = this.form.invoiceFiles || []
      } else {
        // 新增模式
        this.form = {
          id: null,
          applicantId: null,
          applicantName: '',
          applicationDate: this.$moment(),
          reimbursementType: '',
          amount: 0,
          expenseDate: null,
          expenseDescription: '',
          invoiceFiles: [],
          remark: ''
        }
        this.fileList = []
      }
    },

    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true

          // 处理日期格式
          const formData = { ...this.form }
          if (formData.applicationDate) {
            formData.applicationDate = formData.applicationDate.format('YYYY-MM-DD')
          }
          if (formData.expenseDate) {
            formData.expenseDate = formData.expenseDate.format('YYYY-MM-DD')
          }
          
          // 处理文件列表
          formData.invoiceFiles = this.fileList

          // 调用API保存数据
          const apiCall = this.form.id ? updateReimbursement : addReimbursement
          apiCall(formData).then(res => {
            if (res.success) {
              this.$emit('ok', formData)
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.message || '保存失败')
            }
          }).finally(() => {
            this.confirmLoading = false
          })
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}
</style>
