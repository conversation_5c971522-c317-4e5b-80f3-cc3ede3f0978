<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <!-- 员工信息区域 -->
      <a-divider orientation="left">员工基本信息</a-divider>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="关联用户" prop="userId">
            <a-select
              v-model="form.userId"
              placeholder="请选择关联用户"
              showSearch
              optionFilterProp="children"
              @change="handleUserChange">
              <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
                {{ user.userName }} ({{ user.realName }})
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="员工编号" prop="employeeCode">
            <a-input v-model="form.employeeCode" placeholder="请输入员工编号" />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="员工姓名" prop="employeeName">
            <a-input v-model="form.employeeName" placeholder="请输入员工姓名" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="联系电话" prop="phone">
            <a-input v-model="form.phone" placeholder="请输入联系电话" />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="部门" prop="department">
            <a-select v-model="form.department" placeholder="请选择部门">
              <a-select-option value="珐琅制作">珐琅制作</a-select-option>
              <a-select-option value="咖啡服务">咖啡服务</a-select-option>
              <a-select-option value="培训教学">培训教学</a-select-option>
              <a-select-option value="业务拓展">业务拓展</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="职位" prop="position">
            <a-input v-model="form.position" placeholder="请输入职位" />
          </a-form-model-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-model-item label="入职时间" prop="entryDate">
            <a-date-picker
              v-model="form.entryDate"
              format="YYYY-MM-DD"
              placeholder="请选择入职时间"
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item label="状态" prop="salaryStatus">
            <a-select v-model="form.salaryStatus" placeholder="请选择状态">
              <a-select-option value="ACTIVE">生效</a-select-option>
              <a-select-option value="INACTIVE">失效</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>

      <!-- 薪酬项目配置区域 -->
      <a-divider orientation="left">薪酬项目配置</a-divider>

      <div class="salary-items-config">
        <a-alert
          message="薪酬项目配置"
          description="为该员工选择适用的薪酬项目，可以使用系统默认值或设置个人特殊比例"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />

        <a-table
          :columns="salaryItemColumns"
          :data-source="salaryItems"
          :pagination="false"
          size="small"
          bordered>
          <template slot="enabled" slot-scope="text, record, index">
            <a-checkbox
              v-model="record.enabled"
              @change="handleItemEnabledChange(record, index)">
            </a-checkbox>
          </template>

          <template slot="value" slot-scope="text, record, index">
            <div v-if="record.enabled">
              <a-radio-group
                v-model="record.useSystemDefault"
                @change="handleValueTypeChange(record, index)"
                size="small">
                <a-radio :value="true">系统默认</a-radio>
                <a-radio :value="false">自定义</a-radio>
              </a-radio-group>

              <div style="margin-top: 8px;" v-if="!record.useSystemDefault">
                <a-input-number
                  v-model="record.customValue"
                  :min="0"
                  :precision="record.valueType === 'RATE' ? 3 : 2"
                  :step="record.valueType === 'RATE' ? 0.001 : 1"
                  style="width: 120px"
                />
                <span class="input-suffix">{{ record.unit }}</span>
              </div>

              <div v-else style="margin-top: 8px;">
                <span class="system-default-value">
                  {{ record.systemDefaultValue }}{{ record.unit }}
                </span>
              </div>
            </div>
            <span v-else class="disabled-text">未启用</span>
          </template>
        </a-table>
      </div>

      <a-form-model-item label="备注" prop="remark">
        <a-textarea
          v-model="form.remark"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { getUserList } from '@/api/api'

export default {
  name: 'SalaryProfileModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      confirmLoading: false,
      userList: [],
      form: {
        id: null,
        userId: null,
        employeeName: '',
        employeeCode: '',
        phone: '',
        department: '',
        position: '',
        entryDate: null,
        salaryStatus: 'ACTIVE',
        remark: ''
      },

      // 个人薪酬项目配置（员工可个性化设置）
      salaryItems: [
        {
          itemCode: 'DAILY_WAGE',
          itemName: '基础日薪',
          category: '固定薪酬',
          valueType: 'AMOUNT',
          unit: '元/天',
          enabled: true,
          useSystemDefault: true,
          customValue: 200,
          systemDefaultValue: 200,
          description: '员工基础日薪标准'
        },
        {
          itemCode: 'COFFEE_COMMISSION',
          itemName: '咖啡店销售提成',
          category: '销售提成',
          valueType: 'RATE',
          unit: '%',
          enabled: false,
          useSystemDefault: true,
          customValue: 0.05,
          systemDefaultValue: 0.05,
          description: '按当天咖啡店销售额计算提成'
        },
        {
          itemCode: 'EXTERNAL_INSTRUCTOR',
          itemName: '外出讲师费',
          category: '销售提成',
          valueType: 'AMOUNT',
          unit: '元/次',
          enabled: false,
          useSystemDefault: true,
          customValue: 800,
          systemDefaultValue: 800,
          description: '外出培训讲师费用'
        },
        {
          itemCode: 'INTERNAL_INSTRUCTOR',
          itemName: '在馆讲师费',
          category: '销售提成',
          valueType: 'AMOUNT',
          unit: '元/次',
          enabled: false,
          useSystemDefault: true,
          customValue: 500,
          systemDefaultValue: 500,
          description: '在馆培训讲师费用'
        },
        {
          itemCode: 'EXTERNAL_ASSISTANT',
          itemName: '外出助理费',
          category: '销售提成',
          valueType: 'AMOUNT',
          unit: '元/次',
          enabled: false,
          useSystemDefault: true,
          customValue: 400,
          systemDefaultValue: 400,
          description: '外出培训助理费用'
        },
        {
          itemCode: 'INTERNAL_ASSISTANT',
          itemName: '在馆助理费',
          category: '销售提成',
          valueType: 'AMOUNT',
          unit: '元/次',
          enabled: false,
          useSystemDefault: true,
          customValue: 300,
          systemDefaultValue: 300,
          description: '在馆培训助理费用'
        },
        {
          itemCode: 'CHANNEL_DEVELOPMENT',
          itemName: '渠道开发提成',
          category: '销售提成',
          valueType: 'RATE',
          unit: '%',
          enabled: false,
          useSystemDefault: true,
          customValue: 0.08,
          systemDefaultValue: 0.08,
          description: '渠道开发提成比例'
        },
        {
          itemCode: 'HALL_SALES_COMMISSION',
          itemName: '馆内销售提成',
          category: '销售提成',
          valueType: 'RATE',
          unit: '%',
          enabled: false,
          useSystemDefault: true,
          customValue: 0.03,
          systemDefaultValue: 0.03,
          description: '馆内销售提成比例'
        },
        {
          itemCode: 'ARTWORK_SALES',
          itemName: '艺术作品销售',
          category: '销售提成',
          valueType: 'MANUAL',
          unit: '按次填报',
          enabled: false,
          useSystemDefault: true,
          customValue: 0,
          systemDefaultValue: 0,
          description: '艺术作品销售收入，按次填报'
        }
      ],

      // 薪酬项目表格列定义
      salaryItemColumns: [
        {
          title: '分类',
          dataIndex: 'category',
          width: 100
        },
        {
          title: '薪酬项目',
          dataIndex: 'itemName',
          width: 150
        },
        {
          title: '启用',
          dataIndex: 'enabled',
          width: 60,
          align: 'center',
          scopedSlots: { customRender: 'enabled' }
        },
        {
          title: '配置值',
          dataIndex: 'value',
          scopedSlots: { customRender: 'value' }
        },
        {
          title: '说明',
          dataIndex: 'description',
          width: 180,
          ellipsis: true
        }
      ],
      rules: {
        userId: [
          { required: true, message: '请选择关联用户', trigger: 'change' }
        ],
        employeeName: [
          { required: true, message: '请输入员工姓名', trigger: 'blur' }
        ],
        employeeCode: [
          { required: true, message: '请输入员工编号', trigger: 'blur' }
        ],
        department: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        position: [
          { required: true, message: '请输入职位', trigger: 'blur' }
        ],
        entryDate: [
          { required: true, message: '请选择入职时间', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    modalTitle() {
      return this.form.id ? '编辑薪资档案' : '新增薪资档案'
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        this.loadUserList()
      }
    }
  },
  methods: {
    // 加载用户列表
    loadUserList() {
      getUserList().then(res => {
        if (res.success) {
          this.userList = res.result || []
        }
      })
    },

    // 用户选择变化
    handleUserChange(userId) {
      const selectedUser = this.userList.find(user => user.id === userId)
      if (selectedUser) {
        this.form.employeeName = selectedUser.realName || selectedUser.userName
        this.form.phone = selectedUser.phone || ''
      }
    },

    initForm() {
      if (this.record && this.record.id) {
        // 编辑模式
        this.form = { ...this.record }
        if (this.form.entryDate) {
          this.form.entryDate = this.$moment(this.form.entryDate)
        }
        // 加载员工的薪酬项目配置
        this.loadEmployeeSalaryItems()
      } else {
        // 新增模式
        this.form = {
          id: null,
          userId: null,
          employeeName: '',
          employeeCode: '',
          phone: '',
          department: '',
          position: '',
          entryDate: null,
          salaryStatus: 'ACTIVE',
          remark: ''
        }
        // 重置薪酬项目配置
        this.resetSalaryItems()
      }
    },
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true

          // 处理日期格式
          const formData = { ...this.form }
          if (formData.entryDate) {
            formData.entryDate = formData.entryDate.format('YYYY-MM-DD')
          }

          // 添加薪酬项目配置数据
          formData.salaryItems = this.getSalaryItemsData()

          // 这里应该调用API保存数据
          // 暂时模拟异步操作
          setTimeout(() => {
            this.confirmLoading = false
            this.$emit('ok', formData)
            this.$message.success('保存成功')
          }, 1000)
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    },

    // 薪酬项目启用状态变化
    handleItemEnabledChange(record, index) {
      // 如果禁用，重置为系统默认
      if (!record.enabled) {
        record.useSystemDefault = true
        record.customValue = record.systemDefaultValue
      }
    },

    // 值类型变化（系统默认 vs 自定义）
    handleValueTypeChange(record, index) {
      if (record.useSystemDefault) {
        record.customValue = record.systemDefaultValue
      }
    },

    // 重置薪酬项目配置
    resetSalaryItems() {
      this.salaryItems.forEach(item => {
        item.enabled = item.itemCode === 'DAILY_WAGE' // 只有基础日薪默认启用
        item.useSystemDefault = true
        item.customValue = item.systemDefaultValue
      })
    },

    // 获取薪酬项目配置数据
    getSalaryItemsData() {
      return this.salaryItems.filter(item => item.enabled).map(item => ({
        itemCode: item.itemCode,
        itemName: item.itemName,
        category: item.category,
        valueType: item.valueType,
        useSystemDefault: item.useSystemDefault,
        value: item.useSystemDefault ? item.systemDefaultValue : item.customValue
      }))
    },

    // 加载员工薪酬项目配置
    loadEmployeeSalaryItems() {
      // TODO: 从后端加载员工的薪酬项目配置
      // 这里暂时使用模拟数据
      console.log('加载员工薪酬项目配置:', this.form.id)
    }
  }
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}

.salary-items-config {
  margin-top: 16px;
}

.system-default-value {
  color: #1890ff;
  font-weight: 500;
}

.disabled-text {
  color: #ccc;
}

.salary-items-config .ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td {
  padding: 8px;
}
</style>
