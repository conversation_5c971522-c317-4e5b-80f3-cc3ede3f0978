<template>
  <a-modal
    title="报销类型管理"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="type-management">
      <!-- 操作按钮 -->
      <div class="table-operator" style="margin-bottom: 16px;">
        <a-button type="primary" @click="handleAdd" icon="plus">新增类型</a-button>
        <a-tooltip placement="left" title="报销类型定义了员工可申请的报销项目，包括交通费、餐费、材料费等。">
          <a-icon type="question-circle" style="font-size:16px;float:right;" />
        </a-tooltip>
      </div>

      <!-- 类型列表 -->
      <a-table
        :columns="columns"
        :data-source="types"
        :pagination="false"
        size="middle"
        bordered
        rowKey="code">
        
        <template slot="limit" slot-scope="text">
          <span style="font-weight: bold; color: #1890ff;">¥{{ text || 0 }}</span>
        </template>
        
        <template slot="status" slot-scope="text">
          <a-tag :color="text === 'ACTIVE' ? 'green' : 'red'">
            {{ text === 'ACTIVE' ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a-button size="small" @click="handleEdit(record)" style="margin-right: 8px;">编辑</a-button>
          <a-button 
            size="small" 
            @click="handleToggleStatus(record)"
            :type="record.status === 'ACTIVE' ? 'danger' : 'primary'">
            {{ record.status === 'ACTIVE' ? '禁用' : '启用' }}
          </a-button>
        </template>
      </a-table>
    </div>

    <!-- 编辑类型弹窗 -->
    <a-modal
      :title="editModalTitle"
      :visible="editModalVisible"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      width="500px">
      <a-form-model
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }">
        
        <a-form-model-item label="类型编码" prop="code">
          <a-input v-model="editForm.code" placeholder="请输入类型编码" :disabled="!!editForm.id" />
        </a-form-model-item>
        
        <a-form-model-item label="类型名称" prop="name">
          <a-input v-model="editForm.name" placeholder="请输入类型名称" />
        </a-form-model-item>
        
        <a-form-model-item label="限额" prop="limit">
          <a-input-number
            v-model="editForm.limit"
            :min="0"
            :precision="2"
            style="width: 100%"
            placeholder="请输入限额" />
          <span class="input-suffix">元</span>
        </a-form-model-item>
        
        <a-form-model-item label="描述" prop="description">
          <a-textarea v-model="editForm.description" :rows="3" placeholder="请输入描述" />
        </a-form-model-item>
        
        <a-form-model-item label="排序" prop="sortOrder">
          <a-input-number v-model="editForm.sortOrder" :min="0" style="width: 100%" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-modal>
</template>

<script>
export default {
  name: 'ReimbursementTypeModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      editModalVisible: false,
      editModalTitle: '新增类型',
      
      // 类型数据
      types: [
        { id: 1, code: 'TRAVEL', name: '交通费', limit: 1000, status: 'ACTIVE', sortOrder: 1, description: '差旅、通勤等交通费用' },
        { id: 2, code: 'MEAL', name: '餐费', limit: 500, status: 'ACTIVE', sortOrder: 2, description: '工作餐、招待费等' },
        { id: 3, code: 'MATERIAL', name: '材料费', limit: 2000, status: 'ACTIVE', sortOrder: 3, description: '工作用品、原材料等' },
        { id: 4, code: 'ACCOMMODATION', name: '住宿费', limit: 800, status: 'ACTIVE', sortOrder: 4, description: '出差住宿费用' },
        { id: 5, code: 'OTHER', name: '其他', limit: 500, status: 'ACTIVE', sortOrder: 5, description: '其他类型费用' }
      ],
      
      // 表格列定义
      columns: [
        { title: '类型编码', dataIndex: 'code', width: 120 },
        { title: '类型名称', dataIndex: 'name', width: 120 },
        { title: '限额', dataIndex: 'limit', width: 100, scopedSlots: { customRender: 'limit' } },
        { title: '状态', dataIndex: 'status', width: 80, scopedSlots: { customRender: 'status' } },
        { title: '排序', dataIndex: 'sortOrder', width: 80 },
        { title: '描述', dataIndex: 'description', ellipsis: true },
        { title: '操作', key: 'action', width: 150, scopedSlots: { customRender: 'action' } }
      ],
      
      // 编辑表单
      editForm: {
        id: null,
        code: '',
        name: '',
        limit: 0,
        description: '',
        sortOrder: 0
      },
      
      // 验证规则
      editRules: {
        code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        limit: [{ required: true, message: '请输入限额', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 新增类型
    handleAdd() {
      this.editForm = {
        id: null,
        code: '',
        name: '',
        limit: 0,
        description: '',
        sortOrder: this.types.length + 1
      }
      this.editModalTitle = '新增类型'
      this.editModalVisible = true
    },
    
    // 编辑类型
    handleEdit(record) {
      this.editForm = { ...record }
      this.editModalTitle = '编辑类型'
      this.editModalVisible = true
    },
    
    // 切换状态
    handleToggleStatus(record) {
      const newStatus = record.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
      record.status = newStatus
      this.$message.success(`${newStatus === 'ACTIVE' ? '启用' : '禁用'}成功`)
    },
    
    // 编辑确定
    handleEditOk() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          if (this.editForm.id) {
            // 更新
            const index = this.types.findIndex(t => t.id === this.editForm.id)
            if (index !== -1) {
              this.types.splice(index, 1, { ...this.editForm })
            }
          } else {
            // 新增
            this.editForm.id = Date.now()
            this.editForm.status = 'ACTIVE'
            this.types.push({ ...this.editForm })
          }
          this.editModalVisible = false
          this.$message.success('保存成功')
        }
      })
    },
    
    // 编辑取消
    handleEditCancel() {
      this.editModalVisible = false
    },
    
    handleOk() {
      this.$emit('ok', this.types)
    },
    
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.type-management {
  max-height: 400px;
  overflow-y: auto;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}
</style>
