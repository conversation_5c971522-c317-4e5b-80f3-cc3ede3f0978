<template>
  <a-modal
    title="数据匹配逻辑设置"
    :visible="visible"
    :width="900"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="matching-logic">
      <a-alert
        message="数据匹配逻辑设置"
        description="配置薪酬计算时如何从各业务模块获取数据，设置匹配规则和数据验证条件。"
        type="warning"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 员工匹配规则 -->
        <a-divider orientation="left">员工匹配规则</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="员工匹配字段" prop="employeeField">
              <a-select v-model="form.employeeField" placeholder="选择员工匹配字段">
                <a-select-option value="employee_id">员工ID</a-select-option>
                <a-select-option value="employee_code">员工编号</a-select-option>
                <a-select-option value="user_id">用户ID</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="匹配方式" prop="employeeMatchType">
              <a-radio-group v-model="form.employeeMatchType">
                <a-radio value="EXACT">精确匹配</a-radio>
                <a-radio value="FUZZY">模糊匹配</a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 时间匹配规则 -->
        <a-divider orientation="left">时间匹配规则</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="时间匹配字段" prop="timeField">
              <a-select v-model="form.timeField" placeholder="选择时间匹配字段">
                <a-select-option value="record_date">记录日期</a-select-option>
                <a-select-option value="create_time">创建时间</a-select-option>
                <a-select-option value="update_time">更新时间</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="时间格式" prop="timeFormat">
              <a-select v-model="form.timeFormat" placeholder="选择时间格式">
                <a-select-option value="YYYY-MM">年月格式</a-select-option>
                <a-select-option value="YYYY-MM-DD">年月日格式</a-select-option>
                <a-select-option value="TIMESTAMP">时间戳格式</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 数据状态筛选 -->
        <a-divider orientation="left">数据状态筛选</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="销售数据状态" prop="salesStatus">
              <a-select v-model="form.salesStatus" mode="multiple" placeholder="选择有效的销售数据状态">
                <a-select-option value="CONFIRMED">已确认</a-select-option>
                <a-select-option value="PAID">已付款</a-select-option>
                <a-select-option value="COMPLETED">已完成</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训数据状态" prop="trainingStatus">
              <a-select v-model="form.trainingStatus" mode="multiple" placeholder="选择有效的培训数据状态">
                <a-select-option value="COMPLETED">已完成</a-select-option>
                <a-select-option value="CONFIRMED">已确认</a-select-option>
                <a-select-option value="PAID">已结算</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="生产数据状态" prop="productionStatus">
              <a-select v-model="form.productionStatus" mode="multiple" placeholder="选择有效的生产数据状态">
                <a-select-option value="FINISHED">已完成</a-select-option>
                <a-select-option value="QUALITY_PASSED">质检通过</a-select-option>
                <a-select-option value="DELIVERED">已交付</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="报销数据状态" prop="reimbursementStatus">
              <a-select v-model="form.reimbursementStatus" mode="multiple" placeholder="选择有效的报销数据状态">
                <a-select-option value="APPROVED">已审批</a-select-option>
                <a-select-option value="PAID">已支付</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 数据验证规则 -->
        <a-divider orientation="left">数据验证规则</a-divider>

        <a-form-model-item label="必填字段验证" prop="requiredFields">
          <a-checkbox-group v-model="form.requiredFields">
            <a-row>
              <a-col :span="6"><a-checkbox value="employee_id">员工ID</a-checkbox></a-col>
              <a-col :span="6"><a-checkbox value="amount">金额</a-checkbox></a-col>
              <a-col :span="6"><a-checkbox value="record_date">记录日期</a-checkbox></a-col>
              <a-col :span="6"><a-checkbox value="status">状态</a-checkbox></a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-model-item>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="金额范围验证" prop="amountValidation">
              <a-switch v-model="form.amountValidation" />
              <span style="margin-left: 8px;">启用金额合理性检查</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="时间范围验证" prop="timeValidation">
              <a-switch v-model="form.timeValidation" />
              <span style="margin-left: 8px;">启用时间范围检查</span>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="form.amountValidation">
          <a-col :span="12">
            <a-form-model-item label="最小金额" prop="minAmount">
              <a-input-number
                v-model="form.minAmount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="最小有效金额"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="最大金额" prop="maxAmount">
              <a-input-number
                v-model="form.maxAmount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="最大有效金额"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 异常处理规则 -->
        <a-divider orientation="left">异常处理规则</a-divider>

        <a-form-model-item label="数据缺失处理" prop="missingDataAction">
          <a-radio-group v-model="form.missingDataAction">
            <a-radio value="SKIP">跳过该员工</a-radio>
            <a-radio value="WARNING">警告并继续</a-radio>
            <a-radio value="ERROR">停止计算</a-radio>
            <a-radio value="DEFAULT">使用默认值</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="数据冲突处理" prop="conflictDataAction">
          <a-radio-group v-model="form.conflictDataAction">
            <a-radio value="LATEST">使用最新数据</a-radio>
            <a-radio value="EARLIEST">使用最早数据</a-radio>
            <a-radio value="SUM">数据求和</a-radio>
            <a-radio value="MANUAL">手动处理</a-radio>
          </a-radio-group>
        </a-form-model-item>

        <a-form-model-item label="启用数据去重" prop="enableDeduplication">
          <a-switch v-model="form.enableDeduplication" />
          <span style="margin-left: 8px;">自动去除重复数据</span>
        </a-form-model-item>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'MatchingLogicModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      form: {
        // 员工匹配规则
        employeeField: 'employee_id',
        employeeMatchType: 'EXACT',
        
        // 时间匹配规则
        timeField: 'record_date',
        timeFormat: 'YYYY-MM',
        
        // 数据状态筛选
        salesStatus: ['CONFIRMED', 'PAID'],
        trainingStatus: ['COMPLETED', 'CONFIRMED'],
        productionStatus: ['FINISHED', 'QUALITY_PASSED'],
        reimbursementStatus: ['APPROVED'],
        
        // 数据验证规则
        requiredFields: ['employee_id', 'amount', 'record_date'],
        amountValidation: true,
        timeValidation: true,
        minAmount: 0.01,
        maxAmount: 50000,
        
        // 异常处理规则
        missingDataAction: 'WARNING',
        conflictDataAction: 'LATEST',
        enableDeduplication: true
      },
      rules: {
        employeeField: [{ required: true, message: '请选择员工匹配字段', trigger: 'change' }],
        timeField: [{ required: true, message: '请选择时间匹配字段', trigger: 'change' }],
        timeFormat: [{ required: true, message: '请选择时间格式', trigger: 'change' }],
        salesStatus: [{ required: true, message: '请选择销售数据状态', trigger: 'change' }],
        trainingStatus: [{ required: true, message: '请选择培训数据状态', trigger: 'change' }],
        productionStatus: [{ required: true, message: '请选择生产数据状态', trigger: 'change' }],
        reimbursementStatus: [{ required: true, message: '请选择报销数据状态', trigger: 'change' }]
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          
          // 模拟API调用
          setTimeout(() => {
            this.confirmLoading = false
            this.$emit('ok', this.form)
            this.$message.success('数据匹配逻辑设置保存成功')
          }, 1000)
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.matching-logic {
  max-height: 600px;
  overflow-y: auto;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}
</style>
