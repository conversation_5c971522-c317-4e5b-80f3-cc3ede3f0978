<template>
  <a-modal
    title="批量配置工具"
    :visible="visible"
    :width="1000"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="batch-config">
      <a-alert
        message="批量配置工具"
        description="按条件筛选员工，批量设置薪酬规则。支持按部门、职位、入职时间等条件进行批量操作。"
        type="success"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-steps :current="currentStep" style="margin-bottom: 24px;">
        <a-step title="选择员工" description="筛选要配置的员工" />
        <a-step title="配置薪酬" description="设置薪酬项目和参数" />
        <a-step title="确认执行" description="预览并执行批量配置" />
      </a-steps>

      <!-- 第一步：选择员工 -->
      <div v-if="currentStep === 0">
        <a-card title="员工筛选条件" size="small">
          <a-form layout="inline">
            <a-form-item label="部门">
              <a-select v-model="filterForm.department" placeholder="选择部门" style="width: 150px;" allow-clear>
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="珐琅制作">珐琅制作</a-select-option>
                <a-select-option value="咖啡服务">咖啡服务</a-select-option>
                <a-select-option value="培训教学">培训教学</a-select-option>
                <a-select-option value="业务拓展">业务拓展</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="职位">
              <a-input v-model="filterForm.position" placeholder="输入职位" style="width: 150px;" />
            </a-form-item>
            <a-form-item label="员工状态">
              <a-select v-model="filterForm.status" placeholder="选择状态" style="width: 120px;" allow-clear>
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="ACTIVE">在职</a-select-option>
                <a-select-option value="PROBATION">试用期</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="handleSearch">筛选</a-button>
              <a-button @click="handleReset" style="margin-left: 8px;">重置</a-button>
            </a-form-item>
          </a-form>
        </a-card>

        <a-card title="员工列表" size="small" style="margin-top: 16px;">
          <a-table
            :columns="employeeColumns"
            :data-source="filteredEmployees"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedEmployees, onChange: onEmployeeSelectChange }"
            size="small"
            bordered>
            
            <template slot="status" slot-scope="text">
              <a-tag :color="text === 'ACTIVE' ? 'green' : 'orange'">
                {{ text === 'ACTIVE' ? '在职' : '试用期' }}
              </a-tag>
            </template>
          </a-table>
          <div style="margin-top: 16px;">
            <span>已选择 {{ selectedEmployees.length }} 名员工</span>
          </div>
        </a-card>
      </div>

      <!-- 第二步：配置薪酬 -->
      <div v-if="currentStep === 1">
        <a-card title="批量薪酬配置" size="small">
          <a-form-model
            ref="configForm"
            :model="configForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
            
            <a-form-model-item label="配置模式">
              <a-radio-group v-model="configForm.mode">
                <a-radio value="REPLACE">替换模式</a-radio>
                <a-radio value="MERGE">合并模式</a-radio>
                <a-radio value="UPDATE">更新模式</a-radio>
              </a-radio-group>
              <div class="config-tip">
                替换：完全替换现有配置；合并：保留现有配置，添加新配置；更新：只更新指定项目
              </div>
            </a-form-model-item>

            <a-divider>薪酬项目配置</a-divider>

            <a-form-model-item label="启用的薪酬项目">
              <a-checkbox-group v-model="configForm.enabledItems">
                <a-row>
                  <a-col :span="8" v-for="item in salaryItems" :key="item.code">
                    <a-checkbox :value="item.code">{{ item.name }}</a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </a-form-model-item>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-model-item label="基础日薪">
                  <a-input-number
                    v-model="configForm.dailyWage"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    placeholder="统一设置日薪"
                  />
                  <span class="input-suffix">元/天</span>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="咖啡店提成">
                  <a-input-number
                    v-model="configForm.coffeeCommission"
                    :min="0"
                    :max="1"
                    :precision="3"
                    style="width: 100%"
                    placeholder="统一设置提成比例"
                  />
                  <span class="input-suffix">%</span>
                </a-form-model-item>
              </a-col>
            </a-row>

            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-model-item label="外出讲师费">
                  <a-input-number
                    v-model="configForm.externalInstructor"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    placeholder="统一设置讲师费"
                  />
                  <span class="input-suffix">元/次</span>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="在馆讲师费">
                  <a-input-number
                    v-model="configForm.internalInstructor"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    placeholder="统一设置讲师费"
                  />
                  <span class="input-suffix">元/次</span>
                </a-form-model-item>
              </a-col>
            </a-row>

            <a-form-model-item label="按部门差异化">
              <a-switch v-model="configForm.departmentDiff" />
              <span style="margin-left: 8px;">不同部门使用不同的配置值</span>
            </a-form-model-item>

            <div v-if="configForm.departmentDiff">
              <a-table
                :columns="departmentConfigColumns"
                :data-source="departmentConfigs"
                :pagination="false"
                size="small"
                bordered>
                
                <template slot="dailyWage" slot-scope="text, record">
                  <a-input-number
                    v-model="record.dailyWage"
                    :min="0"
                    :precision="2"
                    size="small"
                    style="width: 100%"
                  />
                </template>
                
                <template slot="coffeeCommission" slot-scope="text, record">
                  <a-input-number
                    v-model="record.coffeeCommission"
                    :min="0"
                    :max="1"
                    :precision="3"
                    size="small"
                    style="width: 100%"
                  />
                </template>
              </a-table>
            </div>
          </a-form-model>
        </a-card>
      </div>

      <!-- 第三步：确认执行 -->
      <div v-if="currentStep === 2">
        <a-card title="配置预览" size="small">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="影响员工数">{{ selectedEmployees.length }} 人</a-descriptions-item>
            <a-descriptions-item label="配置模式">{{ getModeText(configForm.mode) }}</a-descriptions-item>
            <a-descriptions-item label="启用项目数">{{ configForm.enabledItems.length }} 项</a-descriptions-item>
            <a-descriptions-item label="部门差异化">{{ configForm.departmentDiff ? '是' : '否' }}</a-descriptions-item>
          </a-descriptions>

          <a-divider>影响的员工</a-divider>
          <a-table
            :columns="previewColumns"
            :data-source="previewData"
            :pagination="false"
            size="small"
            bordered>
          </a-table>

          <a-alert
            message="注意事项"
            description="批量配置将影响所选员工的薪酬规则，请确认无误后执行。建议先在测试环境验证配置效果。"
            type="warning"
            show-icon
            style="margin-top: 16px"
          />
        </a-card>
      </div>

      <!-- 步骤控制按钮 -->
      <div style="text-align: center; margin-top: 24px;">
        <a-button v-if="currentStep > 0" @click="handlePrev">上一步</a-button>
        <a-button 
          v-if="currentStep < 2" 
          type="primary" 
          @click="handleNext"
          :disabled="currentStep === 0 && selectedEmployees.length === 0"
          style="margin-left: 8px;">
          下一步
        </a-button>
        <a-button 
          v-if="currentStep === 2" 
          type="primary" 
          @click="handleExecute"
          style="margin-left: 8px;">
          执行配置
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'BatchConfigModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      currentStep: 0,
      
      // 筛选表单
      filterForm: {
        department: '',
        position: '',
        status: ''
      },
      
      // 员工数据
      allEmployees: [
        { id: 1, name: '张三', code: 'E001', department: '珐琅制作', position: '制作师', status: 'ACTIVE' },
        { id: 2, name: '李四', code: 'E002', department: '咖啡服务', position: '咖啡师', status: 'ACTIVE' },
        { id: 3, name: '王五', code: 'E003', department: '培训教学', position: '讲师', status: 'ACTIVE' },
        { id: 4, name: '赵六', code: 'E004', department: '业务拓展', position: '销售', status: 'PROBATION' }
      ],
      filteredEmployees: [],
      selectedEmployees: [],
      
      // 配置表单
      configForm: {
        mode: 'MERGE',
        enabledItems: ['DAILY_WAGE'],
        dailyWage: null,
        coffeeCommission: null,
        externalInstructor: null,
        internalInstructor: null,
        departmentDiff: false
      },
      
      // 薪酬项目
      salaryItems: [
        { code: 'DAILY_WAGE', name: '基础日薪' },
        { code: 'COFFEE_COMMISSION', name: '咖啡店提成' },
        { code: 'EXTERNAL_INSTRUCTOR', name: '外出讲师费' },
        { code: 'INTERNAL_INSTRUCTOR', name: '在馆讲师费' },
        { code: 'EXTERNAL_ASSISTANT', name: '外出助理费' },
        { code: 'INTERNAL_ASSISTANT', name: '在馆助理费' }
      ],
      
      // 部门配置
      departmentConfigs: [
        { department: '珐琅制作', dailyWage: 250, coffeeCommission: 0 },
        { department: '咖啡服务', dailyWage: 180, coffeeCommission: 0.05 },
        { department: '培训教学', dailyWage: 300, coffeeCommission: 0 },
        { department: '业务拓展', dailyWage: 220, coffeeCommission: 0.03 }
      ],
      
      // 表格列定义
      employeeColumns: [
        { title: '员工编号', dataIndex: 'code', width: 100 },
        { title: '员工姓名', dataIndex: 'name', width: 100 },
        { title: '部门', dataIndex: 'department', width: 100 },
        { title: '职位', dataIndex: 'position', width: 100 },
        { title: '状态', dataIndex: 'status', width: 80, scopedSlots: { customRender: 'status' } }
      ],
      
      departmentConfigColumns: [
        { title: '部门', dataIndex: 'department', width: 120 },
        { title: '日薪(元)', dataIndex: 'dailyWage', width: 120, scopedSlots: { customRender: 'dailyWage' } },
        { title: '咖啡店提成(%)', dataIndex: 'coffeeCommission', width: 150, scopedSlots: { customRender: 'coffeeCommission' } }
      ],
      
      previewColumns: [
        { title: '员工姓名', dataIndex: 'name', width: 100 },
        { title: '部门', dataIndex: 'department', width: 100 },
        { title: '当前配置项', dataIndex: 'currentItems', width: 150 },
        { title: '新配置项', dataIndex: 'newItems', width: 150 },
        { title: '变更说明', dataIndex: 'changes', ellipsis: true }
      ]
    }
  },
  computed: {
    previewData() {
      return this.selectedEmployees.map(id => {
        const employee = this.allEmployees.find(e => e.id === id)
        return {
          name: employee.name,
          department: employee.department,
          currentItems: '基础日薪, 咖啡店提成',
          newItems: this.configForm.enabledItems.map(code => {
            const item = this.salaryItems.find(item => item.code === code)
            return item ? item.name : ''
          }).join(', '),
          changes: this.getModeText(this.configForm.mode) + '配置'
        }
      })
    }
  },
  mounted() {
    this.filteredEmployees = [...this.allEmployees]
  },
  methods: {
    // 员工筛选
    handleSearch() {
      this.filteredEmployees = this.allEmployees.filter(emp => {
        return (!this.filterForm.department || emp.department === this.filterForm.department) &&
               (!this.filterForm.position || emp.position.includes(this.filterForm.position)) &&
               (!this.filterForm.status || emp.status === this.filterForm.status)
      })
    },
    
    handleReset() {
      this.filterForm = { department: '', position: '', status: '' }
      this.filteredEmployees = [...this.allEmployees]
    },
    
    onEmployeeSelectChange(selectedRowKeys) {
      this.selectedEmployees = selectedRowKeys
    },
    
    // 步骤控制
    handleNext() {
      this.currentStep++
    },
    
    handlePrev() {
      this.currentStep--
    },
    
    // 执行配置
    handleExecute() {
      this.confirmLoading = true
      
      // 模拟API调用
      setTimeout(() => {
        this.confirmLoading = false
        this.$emit('ok', {
          employees: this.selectedEmployees,
          config: this.configForm
        })
        this.$message.success(`成功为 ${this.selectedEmployees.length} 名员工配置薪酬规则`)
        this.currentStep = 0
        this.selectedEmployees = []
      }, 2000)
    },
    
    getModeText(mode) {
      const modeMap = {
        'REPLACE': '替换',
        'MERGE': '合并',
        'UPDATE': '更新'
      }
      return modeMap[mode] || mode
    },
    
    handleOk() {
      this.handleExecute()
    },

    handleCancel() {
      this.currentStep = 0
      this.selectedEmployees = []
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.batch-config {
  max-height: 600px;
  overflow-y: auto;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.config-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style>
