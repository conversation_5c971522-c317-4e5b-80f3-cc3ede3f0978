<template>
  <a-modal
    title="薪酬项目模板管理"
    :visible="visible"
    :width="900"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="template-management">
      <!-- 操作按钮 -->
      <div class="table-operator" style="margin-bottom: 16px;">
        <a-button type="primary" @click="handleAdd" icon="plus">新增模板</a-button>
        <a-tooltip placement="left" title="薪酬项目模板定义了员工可选择的薪酬项目类型，包括固定薪酬、提成、项目收入等。">
          <a-icon type="question-circle" style="font-size:16px;float:right;" />
        </a-tooltip>
      </div>

      <!-- 模板列表 -->
      <a-table
        :columns="columns"
        :data-source="templates"
        :pagination="false"
        size="middle"
        bordered
        rowKey="code">
        
        <template slot="type" slot-scope="text">
          <a-tag :color="getTypeColor(text)">
            {{ getTypeText(text) }}
          </a-tag>
        </template>
        
        <template slot="status" slot-scope="text">
          <a-tag :color="text === 'ACTIVE' ? 'green' : 'red'">
            {{ text === 'ACTIVE' ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a-button size="small" @click="handleEdit(record)" style="margin-right: 8px;">编辑</a-button>
          <a-button 
            size="small" 
            @click="handleToggleStatus(record)"
            :type="record.status === 'ACTIVE' ? 'danger' : 'primary'">
            {{ record.status === 'ACTIVE' ? '禁用' : '启用' }}
          </a-button>
        </template>
      </a-table>
    </div>

    <!-- 编辑模板弹窗 -->
    <a-modal
      :title="editModalTitle"
      :visible="editModalVisible"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      width="600px">
      <a-form-model
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }">
        
        <a-form-model-item label="项目编码" prop="code">
          <a-input v-model="editForm.code" placeholder="请输入项目编码" :disabled="!!editForm.id" />
        </a-form-model-item>
        
        <a-form-model-item label="项目名称" prop="name">
          <a-input v-model="editForm.name" placeholder="请输入项目名称" />
        </a-form-model-item>
        
        <a-form-model-item label="项目类型" prop="type">
          <a-select v-model="editForm.type" placeholder="请选择项目类型">
            <a-select-option value="FIXED">固定薪酬</a-select-option>
            <a-select-option value="COMMISSION">提成</a-select-option>
            <a-select-option value="PROJECT">项目收入</a-select-option>
            <a-select-option value="MANUAL">手动填报</a-select-option>
          </a-select>
        </a-form-model-item>
        
        <a-form-model-item label="计算方式" prop="calculationType">
          <a-select v-model="editForm.calculationType" placeholder="请选择计算方式">
            <a-select-option value="AMOUNT">固定金额</a-select-option>
            <a-select-option value="RATE">按比例</a-select-option>
            <a-select-option value="TIMES">按次计算</a-select-option>
          </a-select>
        </a-form-model-item>
        
        <a-form-model-item label="默认值" prop="defaultValue">
          <a-input-number
            v-model="editForm.defaultValue"
            :min="0"
            :precision="editForm.calculationType === 'RATE' ? 3 : 2"
            style="width: 100%"
            placeholder="请输入默认值" />
          <span class="input-suffix">{{ getUnitText(editForm.calculationType) }}</span>
        </a-form-model-item>
        
        <a-form-model-item label="描述" prop="description">
          <a-textarea v-model="editForm.description" :rows="3" placeholder="请输入描述" />
        </a-form-model-item>
        
        <a-form-model-item label="排序" prop="sortOrder">
          <a-input-number v-model="editForm.sortOrder" :min="0" style="width: 100%" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </a-modal>
</template>

<script>
export default {
  name: 'SalaryItemTemplateModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      editModalVisible: false,
      editModalTitle: '新增模板',
      
      // 模板数据
      templates: [
        { id: 1, code: 'DAILY_WAGE', name: '基础日薪', type: 'FIXED', calculationType: 'AMOUNT', defaultValue: 200, status: 'ACTIVE', sortOrder: 1, description: '员工基础日薪标准' },
        { id: 2, code: 'COFFEE_COMMISSION', name: '咖啡店提成', type: 'COMMISSION', calculationType: 'RATE', defaultValue: 0.05, status: 'ACTIVE', sortOrder: 2, description: '按当天咖啡店销售额计算提成' },
        { id: 3, code: 'INSTRUCTOR_FEE', name: '讲师费', type: 'PROJECT', calculationType: 'AMOUNT', defaultValue: 800, status: 'ACTIVE', sortOrder: 3, description: '培训讲师费用' },
        { id: 4, code: 'ASSISTANT_FEE', name: '助理费', type: 'PROJECT', calculationType: 'AMOUNT', defaultValue: 400, status: 'ACTIVE', sortOrder: 4, description: '培训助理费用' },
        { id: 5, code: 'CHANNEL_COMMISSION', name: '渠道开发提成', type: 'COMMISSION', calculationType: 'RATE', defaultValue: 0.08, status: 'ACTIVE', sortOrder: 5, description: '渠道开发提成比例' },
        { id: 6, code: 'HALL_SALES', name: '馆内销售提成', type: 'COMMISSION', calculationType: 'RATE', defaultValue: 0.03, status: 'ACTIVE', sortOrder: 6, description: '馆内销售提成比例' },
        { id: 7, code: 'ARTWORK_SALES', name: '艺术作品销售', type: 'MANUAL', calculationType: 'TIMES', defaultValue: 0, status: 'ACTIVE', sortOrder: 7, description: '艺术作品销售收入，按次填报' }
      ],
      
      // 表格列定义
      columns: [
        { title: '项目编码', dataIndex: 'code', width: 150 },
        { title: '项目名称', dataIndex: 'name', width: 150 },
        { title: '项目类型', dataIndex: 'type', width: 100, scopedSlots: { customRender: 'type' } },
        { title: '默认值', dataIndex: 'defaultValue', width: 100 },
        { title: '状态', dataIndex: 'status', width: 80, scopedSlots: { customRender: 'status' } },
        { title: '排序', dataIndex: 'sortOrder', width: 80 },
        { title: '操作', key: 'action', width: 150, scopedSlots: { customRender: 'action' } }
      ],
      
      // 编辑表单
      editForm: {
        id: null,
        code: '',
        name: '',
        type: '',
        calculationType: '',
        defaultValue: 0,
        description: '',
        sortOrder: 0
      },
      
      // 验证规则
      editRules: {
        code: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
        calculationType: [{ required: true, message: '请选择计算方式', trigger: 'change' }],
        defaultValue: [{ required: true, message: '请输入默认值', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 获取类型颜色
    getTypeColor(type) {
      const colorMap = {
        'FIXED': 'blue',
        'COMMISSION': 'green',
        'PROJECT': 'orange',
        'MANUAL': 'purple'
      }
      return colorMap[type] || 'default'
    },
    
    // 获取类型文本
    getTypeText(type) {
      const textMap = {
        'FIXED': '固定薪酬',
        'COMMISSION': '提成',
        'PROJECT': '项目收入',
        'MANUAL': '手动填报'
      }
      return textMap[type] || type
    },
    
    // 获取单位文本
    getUnitText(calculationType) {
      const unitMap = {
        'AMOUNT': '元',
        'RATE': '%',
        'TIMES': '次'
      }
      return unitMap[calculationType] || ''
    },
    
    // 新增模板
    handleAdd() {
      this.editForm = {
        id: null,
        code: '',
        name: '',
        type: '',
        calculationType: '',
        defaultValue: 0,
        description: '',
        sortOrder: this.templates.length + 1
      }
      this.editModalTitle = '新增模板'
      this.editModalVisible = true
    },
    
    // 编辑模板
    handleEdit(record) {
      this.editForm = { ...record }
      this.editModalTitle = '编辑模板'
      this.editModalVisible = true
    },
    
    // 切换状态
    handleToggleStatus(record) {
      const newStatus = record.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
      record.status = newStatus
      this.$message.success(`${newStatus === 'ACTIVE' ? '启用' : '禁用'}成功`)
    },
    
    // 编辑确定
    handleEditOk() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          if (this.editForm.id) {
            // 更新
            const index = this.templates.findIndex(t => t.id === this.editForm.id)
            if (index !== -1) {
              this.templates.splice(index, 1, { ...this.editForm })
            }
          } else {
            // 新增
            this.editForm.id = Date.now()
            this.editForm.status = 'ACTIVE'
            this.templates.push({ ...this.editForm })
          }
          this.editModalVisible = false
          this.$message.success('保存成功')
        }
      })
    },
    
    // 编辑取消
    handleEditCancel() {
      this.editModalVisible = false
    },
    
    handleOk() {
      this.$emit('ok', this.templates)
    },
    
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.template-management {
  max-height: 500px;
  overflow-y: auto;
}

.input-suffix {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}
</style>
