<template>
  <a-modal
    title="审批流程配置"
    :visible="visible"
    :width="900"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="approval-process">
      <a-alert
        message="审批流程配置"
        description="配置报销申请和薪酬计算的审批流程，设置审批层级、审批人员和自动审批规则。"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-tabs v-model="activeTab">
        <!-- 报销审批流程 -->
        <a-tab-pane key="reimbursement" tab="报销审批流程">
          <a-form-model
            ref="reimbursementForm"
            :model="reimbursementProcess"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
            
            <a-form-model-item label="启用审批流程">
              <a-switch v-model="reimbursementProcess.enabled" />
              <span style="margin-left: 8px;">关闭后所有报销申请将自动通过</span>
            </a-form-model-item>

            <div v-if="reimbursementProcess.enabled">
              <a-form-model-item label="审批层级数">
                <a-input-number 
                  v-model="reimbursementProcess.levels" 
                  :min="1" 
                  :max="5"
                  style="width: 200px" />
                <span style="margin-left: 8px;">设置审批需要经过几级审批</span>
              </a-form-model-item>

              <a-divider>审批层级设置</a-divider>

              <div v-for="level in reimbursementProcess.levels" :key="level">
                <a-card size="small" :title="`第${level}级审批`" style="margin-bottom: 16px;">
                  <a-row :gutter="24">
                    <a-col :span="8">
                      <a-form-model-item label="审批人类型">
                        <a-select v-model="reimbursementProcess.levelConfigs[level-1].approverType">
                          <a-select-option value="DIRECT_MANAGER">直接上级</a-select-option>
                          <a-select-option value="DEPARTMENT_MANAGER">部门经理</a-select-option>
                          <a-select-option value="SPECIFIC_USER">指定人员</a-select-option>
                          <a-select-option value="ROLE_BASED">按角色</a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-model-item label="金额阈值">
                        <a-input-number 
                          v-model="reimbursementProcess.levelConfigs[level-1].amountThreshold"
                          :min="0"
                          :precision="2"
                          style="width: 100%" />
                        <div class="config-tip">超过此金额需要此级审批</div>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-model-item label="审批超时(小时)">
                        <a-input-number 
                          v-model="reimbursementProcess.levelConfigs[level-1].timeoutHours"
                          :min="1"
                          :max="168"
                          style="width: 100%" />
                        <div class="config-tip">超时后自动通过或转下级</div>
                      </a-form-model-item>
                    </a-col>
                  </a-row>

                  <a-form-model-item 
                    v-if="reimbursementProcess.levelConfigs[level-1].approverType === 'SPECIFIC_USER'"
                    label="指定审批人">
                    <a-select 
                      v-model="reimbursementProcess.levelConfigs[level-1].specificUsers"
                      mode="multiple"
                      placeholder="选择审批人员">
                      <a-select-option value="admin">管理员</a-select-option>
                      <a-select-option value="manager1">张经理</a-select-option>
                      <a-select-option value="manager2">李经理</a-select-option>
                    </a-select>
                  </a-form-model-item>

                  <a-form-model-item 
                    v-if="reimbursementProcess.levelConfigs[level-1].approverType === 'ROLE_BASED'"
                    label="审批角色">
                    <a-select 
                      v-model="reimbursementProcess.levelConfigs[level-1].approverRoles"
                      mode="multiple"
                      placeholder="选择审批角色">
                      <a-select-option value="DEPARTMENT_MANAGER">部门经理</a-select-option>
                      <a-select-option value="FINANCE_MANAGER">财务经理</a-select-option>
                      <a-select-option value="GENERAL_MANAGER">总经理</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-card>
              </div>

              <a-divider>自动审批规则</a-divider>

              <a-form-model-item label="启用自动审批">
                <a-switch v-model="reimbursementProcess.autoApproval.enabled" />
                <span style="margin-left: 8px;">满足条件的申请自动通过</span>
              </a-form-model-item>

              <div v-if="reimbursementProcess.autoApproval.enabled">
                <a-row :gutter="24">
                  <a-col :span="12">
                    <a-form-model-item label="自动审批金额阈值">
                      <a-input-number 
                        v-model="reimbursementProcess.autoApproval.amountThreshold"
                        :min="0"
                        :precision="2"
                        style="width: 100%" />
                      <div class="config-tip">低于此金额自动通过</div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-model-item label="自动审批类型">
                      <a-select 
                        v-model="reimbursementProcess.autoApproval.allowedTypes"
                        mode="multiple"
                        placeholder="选择可自动审批的类型">
                        <a-select-option value="TRAVEL">交通费</a-select-option>
                        <a-select-option value="MEAL">餐费</a-select-option>
                        <a-select-option value="MATERIAL">材料费</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-model>
        </a-tab-pane>

        <!-- 薪酬审批流程 -->
        <a-tab-pane key="salary" tab="薪酬审批流程">
          <a-form-model
            ref="salaryForm"
            :model="salaryProcess"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
            
            <a-form-model-item label="启用薪酬审批">
              <a-switch v-model="salaryProcess.enabled" />
              <span style="margin-left: 8px;">关闭后薪酬计算结果将自动生效</span>
            </a-form-model-item>

            <div v-if="salaryProcess.enabled">
              <a-form-model-item label="审批模式">
                <a-radio-group v-model="salaryProcess.mode">
                  <a-radio value="BATCH">批量审批</a-radio>
                  <a-radio value="INDIVIDUAL">逐个审批</a-radio>
                </a-radio-group>
                <div class="config-tip">批量：整批薪酬一起审批；逐个：每个员工单独审批</div>
              </a-form-model-item>

              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-model-item label="审批人员">
                    <a-select 
                      v-model="salaryProcess.approvers"
                      mode="multiple"
                      placeholder="选择薪酬审批人员">
                      <a-select-option value="hr_manager">HR经理</a-select-option>
                      <a-select-option value="finance_manager">财务经理</a-select-option>
                      <a-select-option value="general_manager">总经理</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
                <a-col :span="12">
                  <a-form-model-item label="审批超时(小时)">
                    <a-input-number 
                      v-model="salaryProcess.timeoutHours"
                      :min="1"
                      :max="168"
                      style="width: 100%" />
                    <div class="config-tip">超时后自动通过</div>
                  </a-form-model-item>
                </a-col>
              </a-row>

              <a-form-model-item label="自动审批阈值">
                <a-input-number 
                  v-model="salaryProcess.autoApprovalThreshold"
                  :min="0"
                  :precision="2"
                  style="width: 300px" />
                <span style="margin-left: 8px;">低于此金额的薪酬自动审批</span>
              </a-form-model-item>

              <a-form-model-item label="异常处理">
                <a-checkbox-group v-model="salaryProcess.exceptionHandling">
                  <a-checkbox value="AMOUNT_CHANGE">薪酬金额异常变动时需要审批</a-checkbox>
                  <a-checkbox value="NEW_EMPLOYEE">新员工薪酬需要审批</a-checkbox>
                  <a-checkbox value="CALCULATION_ERROR">计算异常时需要审批</a-checkbox>
                </a-checkbox-group>
              </a-form-model-item>
            </div>
          </a-form-model>
        </a-tab-pane>

        <!-- 通知设置 -->
        <a-tab-pane key="notification" tab="通知设置">
          <a-form-model
            :model="notificationSettings"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }">
            
            <a-form-model-item label="启用邮件通知">
              <a-switch v-model="notificationSettings.emailEnabled" />
            </a-form-model-item>

            <a-form-model-item label="启用短信通知">
              <a-switch v-model="notificationSettings.smsEnabled" />
            </a-form-model-item>

            <a-form-model-item label="启用系统通知">
              <a-switch v-model="notificationSettings.systemEnabled" />
            </a-form-model-item>

            <a-divider>通知时机</a-divider>

            <a-form-model-item label="通知事件">
              <a-checkbox-group v-model="notificationSettings.events">
                <a-checkbox value="SUBMIT">提交申请时</a-checkbox>
                <a-checkbox value="APPROVE">审批通过时</a-checkbox>
                <a-checkbox value="REJECT">审批拒绝时</a-checkbox>
                <a-checkbox value="TIMEOUT">审批超时时</a-checkbox>
              </a-checkbox-group>
            </a-form-model-item>

            <a-form-model-item label="通知模板">
              <a-textarea 
                v-model="notificationSettings.template"
                :rows="4"
                placeholder="通知消息模板，支持变量：{申请人}、{金额}、{类型}等" />
            </a-form-model-item>
          </a-form-model>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'ApprovalProcessModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      activeTab: 'reimbursement',
      
      // 报销审批流程
      reimbursementProcess: {
        enabled: true,
        levels: 2,
        levelConfigs: [
          {
            approverType: 'DIRECT_MANAGER',
            amountThreshold: 1000,
            timeoutHours: 24,
            specificUsers: [],
            approverRoles: []
          },
          {
            approverType: 'DEPARTMENT_MANAGER',
            amountThreshold: 5000,
            timeoutHours: 48,
            specificUsers: [],
            approverRoles: []
          }
        ],
        autoApproval: {
          enabled: true,
          amountThreshold: 200,
          allowedTypes: ['TRAVEL', 'MEAL']
        }
      },
      
      // 薪酬审批流程
      salaryProcess: {
        enabled: true,
        mode: 'BATCH',
        approvers: ['hr_manager', 'finance_manager'],
        timeoutHours: 72,
        autoApprovalThreshold: 3000,
        exceptionHandling: ['AMOUNT_CHANGE', 'NEW_EMPLOYEE']
      },
      
      // 通知设置
      notificationSettings: {
        emailEnabled: true,
        smsEnabled: false,
        systemEnabled: true,
        events: ['SUBMIT', 'APPROVE', 'REJECT'],
        template: '您有一个{类型}申请需要审批，申请人：{申请人}，金额：{金额}元。'
      }
    }
  },
  watch: {
    'reimbursementProcess.levels'(newVal) {
      // 动态调整审批层级配置
      while (this.reimbursementProcess.levelConfigs.length < newVal) {
        this.reimbursementProcess.levelConfigs.push({
          approverType: 'DIRECT_MANAGER',
          amountThreshold: 1000,
          timeoutHours: 24,
          specificUsers: [],
          approverRoles: []
        })
      }
      if (this.reimbursementProcess.levelConfigs.length > newVal) {
        this.reimbursementProcess.levelConfigs = this.reimbursementProcess.levelConfigs.slice(0, newVal)
      }
    }
  },
  methods: {
    handleOk() {
      this.confirmLoading = true
      
      // 模拟API调用
      setTimeout(() => {
        this.confirmLoading = false
        this.$emit('ok', {
          reimbursementProcess: this.reimbursementProcess,
          salaryProcess: this.salaryProcess,
          notificationSettings: this.notificationSettings
        })
        this.$message.success('审批流程配置保存成功')
      }, 1000)
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.approval-process {
  max-height: 600px;
  overflow-y: auto;
}

.config-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style>
