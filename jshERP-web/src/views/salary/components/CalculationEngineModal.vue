<template>
  <a-modal
    title="计算引擎参数配置"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="engine-config">
      <a-alert
        message="计算引擎参数配置"
        description="配置薪酬计算的性能参数、数据源和计算规则，确保计算过程的稳定性和准确性。"
        type="info"
        show-icon
        style="margin-bottom: 24px"
      />

      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }"
      >
        <!-- 性能参数配置 -->
        <a-divider orientation="left">性能参数配置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="批量计算大小" prop="batchSize">
              <a-input-number
                v-model="form.batchSize"
                :min="10"
                :max="500"
                style="width: 100%"
                placeholder="一次计算的员工数量"
              />
              <div class="param-tip">建议值：50-100，过大可能导致内存不足</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="计算超时时间" prop="timeout">
              <a-input-number
                v-model="form.timeout"
                :min="60"
                :max="1800"
                style="width: 100%"
                placeholder="单次计算超时时间(秒)"
              />
              <div class="param-tip">建议值：300秒，防止长时间计算阻塞</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="最大并发数" prop="maxConcurrency">
              <a-input-number
                v-model="form.maxConcurrency"
                :min="1"
                :max="20"
                style="width: 100%"
                placeholder="同时计算的线程数"
              />
              <div class="param-tip">建议值：3-5，避免过度占用系统资源</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="重试次数" prop="retryTimes">
              <a-input-number
                v-model="form.retryTimes"
                :min="0"
                :max="10"
                style="width: 100%"
                placeholder="计算失败时的重试次数"
              />
              <div class="param-tip">建议值：3次，处理网络异常等临时问题</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 数据源配置 -->
        <a-divider orientation="left">数据源配置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="销售数据表" prop="salesTable">
              <a-input v-model="form.salesTable" placeholder="销售记录表名" />
              <div class="param-tip">存储咖啡店销售、馆内销售等数据</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="培训数据表" prop="trainingTable">
              <a-input v-model="form.trainingTable" placeholder="培训记录表名" />
              <div class="param-tip">存储讲师费、助理费等培训数据</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="生产数据表" prop="productionTable">
              <a-input v-model="form.productionTable" placeholder="生产记录表名" />
              <div class="param-tip">存储掐丝点蓝、配饰制作等数据</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="报销数据表" prop="reimbursementTable">
              <a-input v-model="form.reimbursementTable" placeholder="报销记录表名" />
              <div class="param-tip">存储已审批的报销申请数据</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 计算规则配置 -->
        <a-divider orientation="left">计算规则配置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="数据截止日期" prop="dataDeadline">
              <a-input-number
                v-model="form.dataDeadline"
                :min="1"
                :max="31"
                style="width: 100%"
                placeholder="每月数据截止日期"
              />
              <div class="param-tip">建议值：25号，给数据录入留出时间</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="自动审批阈值" prop="approveThreshold">
              <a-input-number
                v-model="form.approveThreshold"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="自动审批的金额阈值"
              />
              <div class="param-tip">低于此金额的薪酬可自动审批</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-form-model-item label="启用自动审批" prop="autoApprove">
          <a-switch v-model="form.autoApprove" />
          <span style="margin-left: 8px;">启用后，低于阈值的薪酬将自动审批</span>
        </a-form-model-item>

        <a-form-model-item label="数据验证模式" prop="validationMode">
          <a-radio-group v-model="form.validationMode">
            <a-radio value="STRICT">严格模式</a-radio>
            <a-radio value="NORMAL">普通模式</a-radio>
            <a-radio value="LOOSE">宽松模式</a-radio>
          </a-radio-group>
          <div class="param-tip">
            严格模式：数据缺失时停止计算；普通模式：数据缺失时警告；宽松模式：忽略数据缺失
          </div>
        </a-form-model-item>

        <!-- 缓存配置 -->
        <a-divider orientation="left">缓存配置</a-divider>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="配置缓存时间" prop="configCacheTtl">
              <a-input-number
                v-model="form.configCacheTtl"
                :min="1"
                :max="168"
                style="width: 100%"
                placeholder="薪酬配置缓存时间(小时)"
              />
              <div class="param-tip">建议值：24小时，减少数据库查询</div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="结果缓存时间" prop="resultCacheTtl">
              <a-input-number
                v-model="form.resultCacheTtl"
                :min="1"
                :max="30"
                style="width: 100%"
                placeholder="计算结果缓存时间(天)"
              />
              <div class="param-tip">建议值：7天，提高查询性能</div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'CalculationEngineModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      form: {
        // 性能参数
        batchSize: 50,
        timeout: 300,
        maxConcurrency: 3,
        retryTimes: 3,
        
        // 数据源配置
        salesTable: 'jsh_sales_record',
        trainingTable: 'jsh_training_record',
        productionTable: 'jsh_production_record',
        reimbursementTable: 'jsh_reimbursement',
        
        // 计算规则
        dataDeadline: 25,
        autoApprove: false,
        approveThreshold: 5000,
        validationMode: 'NORMAL',
        
        // 缓存配置
        configCacheTtl: 24,
        resultCacheTtl: 7
      },
      rules: {
        batchSize: [{ required: true, message: '请输入批量计算大小', trigger: 'blur' }],
        timeout: [{ required: true, message: '请输入计算超时时间', trigger: 'blur' }],
        maxConcurrency: [{ required: true, message: '请输入最大并发数', trigger: 'blur' }],
        retryTimes: [{ required: true, message: '请输入重试次数', trigger: 'blur' }],
        salesTable: [{ required: true, message: '请输入销售数据表名', trigger: 'blur' }],
        trainingTable: [{ required: true, message: '请输入培训数据表名', trigger: 'blur' }],
        productionTable: [{ required: true, message: '请输入生产数据表名', trigger: 'blur' }],
        reimbursementTable: [{ required: true, message: '请输入报销数据表名', trigger: 'blur' }],
        dataDeadline: [{ required: true, message: '请输入数据截止日期', trigger: 'blur' }],
        approveThreshold: [{ required: true, message: '请输入自动审批阈值', trigger: 'blur' }],
        configCacheTtl: [{ required: true, message: '请输入配置缓存时间', trigger: 'blur' }],
        resultCacheTtl: [{ required: true, message: '请输入结果缓存时间', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleOk() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.confirmLoading = true
          
          // 模拟API调用
          setTimeout(() => {
            this.confirmLoading = false
            this.$emit('ok', this.form)
            this.$message.success('计算引擎参数配置保存成功')
          }, 1000)
        }
      })
    },

    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.engine-config {
  max-height: 600px;
  overflow-y: auto;
}

.param-tip {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.ant-divider {
  margin: 16px 0;
  font-weight: bold;
  color: #1890ff;
}
</style>
