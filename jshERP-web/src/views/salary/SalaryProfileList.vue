<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <!-- 搜索区域 -->
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="24">
            <a-form-item label="员工姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入员工姓名" v-model="queryParam.employeeName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="部门" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select placeholder="请选择部门" showSearch allow-clear optionFilterProp="children" v-model="queryParam.department">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="珐琅制作">珐琅制作</a-select-option>
                <a-select-option value="咖啡服务">咖啡服务</a-select-option>
                <a-select-option value="培训教学">培训教学</a-select-option>
                <a-select-option value="业务拓展">业务拓展</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="职位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入职位" v-model="queryParam.position"></a-input>
            </a-form-item>
          </a-col>
          <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
            <a-col :md="6" :sm="24">
              <a-button type="primary" @click="searchQuery">查询</a-button>
              <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </a-col>
          </span>
        </a-row>
        <template v-if="toggleSearchStatus">
          <a-row :gutter="24">
            <a-col :md="6" :sm="24">
              <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-select placeholder="请选择状态" allow-clear v-model="queryParam.salaryStatus">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="ACTIVE">生效</a-select-option>
                  <a-select-option value="INACTIVE">失效</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="入职时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-range-picker
                  style="width:100%"
                  v-model="queryParam.entryDateRange"
                  format="YYYY-MM-DD"
                  :placeholder="['开始时间', '结束时间']"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </template>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator" style="margin-top: 5px">
      <a-button @click="handleAdd" type="primary" icon="plus">新增薪酬档案</a-button>
      <a-button icon="delete" @click="batchDel">删除</a-button>
      <a-button @click="handleBatchConfig" icon="setting">批量配置</a-button>
      <a-button @click="handleExport" icon="download">导出</a-button>
      <a-popover trigger="click" placement="right">
        <template slot="content">
          <a-checkbox-group @change="onColChange" v-model="settingDataIndex" :defaultValue="settingDataIndex">
            <a-row style="width: 500px">
              <template v-for="(item,index) in defColumns">
                <template>
                  <a-col :span="8">
                    <a-checkbox :value="item.dataIndex">
                      <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                    </a-checkbox>
                  </a-col>
                </template>
              </template>
            </a-row>
            <a-row style="padding-top: 10px;">
              <a-col>
                恢复默认列配置：<a-button @click="handleRestDefault" type="link" size="small">恢复默认</a-button>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </template>
        <a-button icon="setting">列设置</a-button>
      </a-popover>
      <a-tooltip placement="left" title="薪酬档案管理用于配置员工的基本薪酬信息和计算规则。
      可以设置日薪标准、提成比例等。支持批量操作和数据导出功能。" slot="action">
        <a-icon type="question-circle" style="font-size:20px;float:right;" />
      </a-tooltip>
    </div>
    
    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">
        
        <template slot="salaryStatus" slot-scope="text">
          <a-tag :color="text === 'ACTIVE' ? 'green' : 'red'">
            {{ text === 'ACTIVE' ? '生效' : '失效' }}
          </a-tag>
        </template>
        
        <template slot="dailyWage" slot-scope="text">
          <span>{{ text ? '¥' + text : '-' }}</span>
        </template>
        
        <template slot="action" slot-scope="text, record">
          <a-button size="small" @click="handleEdit(record)" style="margin-right: 8px;">编辑</a-button>
          <a-button size="small" @click="handleConfig(record)" style="margin-right: 8px;">配置薪酬</a-button>
          <a-dropdown>
            <a-menu slot="overlay">
              <a-menu-item @click="handleUpdateStatus(record)">
                {{ record.salaryStatus === 'ACTIVE' ? '禁用' : '启用' }}
              </a-menu-item>
              <a-menu-item @click="handleDelete(record)">删除</a-menu-item>
            </a-menu>
            <a-button size="small">
              更多 <a-icon type="down" />
            </a-button>
          </a-dropdown>
        </template>
      </a-table>
    </div>
    <!-- table区域-end -->
    
    <!-- 新增/编辑弹窗 -->
    <salary-profile-modal 
      :visible="modalVisible" 
      :record="currentRecord"
      @ok="handleModalOk" 
      @cancel="handleModalCancel" />
      
    <!-- 薪酬配置弹窗 -->
    <salary-config-modal
      :visible="configModalVisible"
      :employee-id="currentRecord.employeeId"
      @ok="handleConfigModalOk"
      @cancel="handleConfigModalCancel" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { getSalaryProfileList, deleteSalaryProfile, updateSalaryProfileStatus } from '@/api/salary'
import SalaryProfileModal from './components/SalaryProfileModal'
import SalaryConfigModal from './components/SalaryConfigModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import JEllipsis from '@/components/jeecg/JEllipsis'

export default {
  name: 'SalaryProfileList',
  mixins: [JeecgListMixin],
  components: {
    SalaryProfileModal,
    SalaryConfigModal,
    JEllipsis
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        employeeName: '',
        department: '',
        position: '',
        salaryStatus: '',
        entryDateRange: []
      },

      // 表单布局
      labelCol: {
        span: 5
      },
      wrapperCol: {
        span: 18,
        offset: 1
      },

      // 搜索展开状态
      toggleSearchStatus: false,

      // 表格数据
      dataSource: [],
      loading: false,

      // 列设置
      settingDataIndex: ['action','employeeName','department','position','phone','dailyWage','entryDate','salaryStatus'],
      defDataIndex: ['action','employeeName','department','position','phone','dailyWage','entryDate','salaryStatus'],
      
      // 分页
      ipagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        }
      },
      
      // 默认列
      defColumns: [
        {
          title: '操作',
          dataIndex: 'action',
          align:"center",
          width: 200,
          scopedSlots: { customRender: 'action' }
        },
        {
          title: '员工姓名',
          dataIndex: 'employeeName',
          width: 120,
          ellipsis: true
        },
        {
          title: '部门',
          dataIndex: 'department',
          width: 120,
          ellipsis: true
        },
        {
          title: '职位',
          dataIndex: 'position',
          width: 120,
          ellipsis: true
        },
        {
          title: '联系电话',
          dataIndex: 'phone',
          width: 130
        },
        {
          title: '日薪标准',
          dataIndex: 'dailyWage',
          width: 100,
          scopedSlots: { customRender: 'dailyWage' }
        },
        {
          title: '入职时间',
          dataIndex: 'entryDate',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'salaryStatus',
          width: 80,
          align: "center",
          scopedSlots: { customRender: 'salaryStatus' }
        }
      ],
      
      // 行选择
      selectedRowKeys: [],
      
      // 弹窗状态
      modalVisible: false,
      configModalVisible: false,
      currentRecord: {}
    }
  },

  computed: {
    // 动态列显示
    columns() {
      return this.defColumns.filter(item => this.settingDataIndex.includes(item.dataIndex))
    }
  },

  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据
    loadData() {
      this.loading = true
      const params = {
        ...this.queryParam,
        current: this.ipagination.current,
        size: this.ipagination.pageSize
      }

      getSalaryProfileList(params).then(res => {
        if (res.success) {
          this.dataSource = res.data.records || []
          this.ipagination.total = res.data.total || 0
        }
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 搜索
    searchQuery() {
      this.ipagination.current = 1
      this.loadData()
    },

    // 重置
    searchReset() {
      this.queryParam = {
        employeeName: '',
        department: '',
        position: '',
        salaryStatus: '',
        entryDateRange: []
      }
      this.searchQuery()
    },

    // 展开/收起搜索
    handleToggleSearch() {
      this.toggleSearchStatus = !this.toggleSearchStatus
    },
    
    // 表格变化
    handleTableChange(pagination) {
      this.ipagination.current = pagination.current
      this.ipagination.pageSize = pagination.pageSize
      this.loadData()
    },
    
    // 新增
    handleAdd() {
      this.currentRecord = {}
      this.modalVisible = true
    },
    
    // 编辑
    handleEdit(record) {
      this.currentRecord = { ...record }
      this.modalVisible = true
    },
    
    // 配置薪酬
    handleConfig(record) {
      this.currentRecord = { ...record }
      this.configModalVisible = true
    },
    
    // 删除
    handleDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除员工 ${record.employeeName} 的薪酬档案吗？`,
        onOk: () => {
          deleteSalaryProfile(record.id).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
        }
      })
    },
    
    // 更新状态
    handleUpdateStatus(record) {
      const newStatus = record.salaryStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
      const action = newStatus === 'ACTIVE' ? '启用' : '禁用'
      
      updateSalaryProfileStatus(record.id, newStatus).then(res => {
        if (res.success) {
          this.$message.success(`${action}成功`)
          this.loadData()
        } else {
          this.$message.error(res.message || `${action}失败`)
        }
      })
    },
    
    // 弹窗确定
    handleModalOk() {
      this.modalVisible = false
      this.loadData()
    },
    
    // 弹窗取消
    handleModalCancel() {
      this.modalVisible = false
    },
    
    // 配置弹窗确定
    handleConfigModalOk() {
      this.configModalVisible = false
      this.$message.success('薪酬配置保存成功')
    },
    
    // 配置弹窗取消
    handleConfigModalCancel() {
      this.configModalVisible = false
    },
    
    // 批量配置
    handleBatchConfig() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要配置的员工')
        return
      }
      // TODO: 实现批量配置功能
      this.$message.info('批量配置功能开发中...')
    },
    
    // 导出
    handleExport() {
      // TODO: 实现导出功能
      this.$message.info('导出功能开发中...')
    },

    // JeecgListMixin需要的方法
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },

    // 批量删除
    batchDel() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的记录')
        return
      }
      this.$confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        onOk: () => {
          // TODO: 实现批量删除API
          this.$message.success('删除成功')
          this.loadData()
        }
      })
    },

    // 列设置变化
    onColChange(checkedValues) {
      this.settingDataIndex = checkedValues
    },

    // 恢复默认列
    handleRestDefault() {
      this.settingDataIndex = [...this.defDataIndex]
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';
</style>
