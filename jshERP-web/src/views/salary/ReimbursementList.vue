<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :bordered="false">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="24">
                <a-form-item label="申请人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入申请人姓名" v-model="queryParam.applicantName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="报销类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select placeholder="请选择报销类型" allow-clear v-model="queryParam.reimbursementType">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="TRAVEL">交通费</a-select-option>
                    <a-select-option value="MEAL">餐费</a-select-option>
                    <a-select-option value="MATERIAL">材料费</a-select-option>
                    <a-select-option value="ACCOMMODATION">住宿费</a-select-option>
                    <a-select-option value="OTHER">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="审批状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select placeholder="请选择状态" allow-clear v-model="queryParam.approvalStatus">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="PENDING">待审批</a-select-option>
                    <a-select-option value="APPROVED">已审批</a-select-option>
                    <a-select-option value="REJECTED">已拒绝</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-col :md="6" :sm="24">
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
                </a-col>
              </span>
            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator" style="margin-top: 5px">
          <a-button 
            type="primary" 
            @click="handleAdd" 
            icon="plus">
            新增报销申请
          </a-button>
          <a-button 
            @click="handleBatchApprove" 
            :disabled="selectedRowKeys.length === 0"
            icon="check">
            批量审批
          </a-button>
          <a-button @click="handleExport" icon="download">导出</a-button>
          <a-tooltip placement="left" title="报销管理用于处理员工的各类报销申请，包括交通费、餐费、材料费等。
          支持在线申请、审批流程和薪酬计算集成。" slot="action">
            <a-icon type="question-circle" style="font-size:20px;float:right;" />
          </a-tooltip>
        </div>
        
        <!-- table区域-begin -->
        <div>
          <a-table
            ref="table"
            size="middle"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
            @change="handleTableChange">
            
            <template slot="reimbursementType" slot-scope="text">
              <a-tag :color="getTypeColor(text)">
                {{ getTypeText(text) }}
              </a-tag>
            </template>
            
            <template slot="amount" slot-scope="text">
              <span style="font-weight: bold; color: #1890ff;">¥{{ text || 0 }}</span>
            </template>
            
            <template slot="approvalStatus" slot-scope="text">
              <a-tag :color="getStatusColor(text)">
                {{ getStatusText(text) }}
              </a-tag>
            </template>
            
            <template slot="action" slot-scope="text, record">
              <a-button size="small" @click="handleView(record)" style="margin-right: 8px;">查看</a-button>
              <a-dropdown v-if="record.approvalStatus === 'PENDING'">
                <a-menu slot="overlay">
                  <a-menu-item @click="handleEdit(record)">编辑</a-menu-item>
                  <a-menu-item @click="handleApprove(record)">审批</a-menu-item>
                  <a-menu-item @click="handleDelete(record)">删除</a-menu-item>
                </a-menu>
                <a-button size="small">
                  操作 <a-icon type="down" />
                </a-button>
              </a-dropdown>
              <a-button 
                v-else-if="record.approvalStatus === 'APPROVED'"
                size="small" 
                @click="handleViewVoucher(record)">
                查看凭证
              </a-button>
            </template>
          </a-table>
        </div>
        <!-- table区域-end -->

        <!-- 报销申请弹窗 -->
        <reimbursement-modal 
          :visible="modalVisible" 
          :record="currentRecord"
          @ok="handleModalOk"
          @cancel="handleModalCancel" />
          
        <!-- 审批弹窗 -->
        <approval-modal 
          :visible="approvalModalVisible" 
          :record="currentRecord"
          @ok="handleApprovalModalOk"
          @cancel="handleApprovalModalCancel" />
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import { getReimbursementList, deleteReimbursement, approveReimbursement } from '@/api/salary'
import ReimbursementModal from './components/ReimbursementModal'
import ApprovalModal from './components/ApprovalModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'ReimbursementList',
  mixins: [JeecgListMixin],
  components: {
    ReimbursementModal,
    ApprovalModal
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        applicantName: '',
        reimbursementType: '',
        approvalStatus: ''
      },
      
      // 表单布局
      labelCol: {
        span: 5
      },
      wrapperCol: {
        span: 18,
        offset: 1
      },
      
      // 表格数据
      dataSource: [],
      loading: false,
      
      // 分页
      ipagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => {
          return range[0] + "-" + range[1] + " 共" + total + "条"
        }
      },
      
      // 表格列定义
      columns: [
        {
          title: '申请单号',
          dataIndex: 'applicationNumber',
          key: 'applicationNumber',
          width: 150
        },
        {
          title: '申请人',
          dataIndex: 'applicantName',
          key: 'applicantName',
          width: 100
        },
        {
          title: '报销类型',
          dataIndex: 'reimbursementType',
          key: 'reimbursementType',
          width: 100,
          scopedSlots: { customRender: 'reimbursementType' }
        },
        {
          title: '报销金额',
          dataIndex: 'amount',
          key: 'amount',
          width: 120,
          scopedSlots: { customRender: 'amount' }
        },
        {
          title: '申请日期',
          dataIndex: 'applicationDate',
          key: 'applicationDate',
          width: 120
        },
        {
          title: '审批状态',
          dataIndex: 'approvalStatus',
          key: 'approvalStatus',
          width: 100,
          scopedSlots: { customRender: 'approvalStatus' }
        },
        {
          title: '审批人',
          dataIndex: 'approverName',
          key: 'approverName',
          width: 100
        },
        {
          title: '审批时间',
          dataIndex: 'approvalDate',
          key: 'approvalDate',
          width: 120
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          scopedSlots: { customRender: 'action' }
        }
      ],
      
      // 行选择
      selectedRowKeys: [],
      
      // 弹窗状态
      modalVisible: false,
      approvalModalVisible: false,
      currentRecord: {}
    }
  },
  
  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据
    loadData() {
      this.loading = true
      const params = {
        ...this.queryParam,
        current: this.ipagination.current,
        size: this.ipagination.pageSize
      }

      getReimbursementList(params).then(res => {
        if (res.success) {
          this.dataSource = res.data.records || []
          this.ipagination.total = res.data.total || 0
        }
      }).finally(() => {
        this.loading = false
      })
    },

    // 搜索
    searchQuery() {
      this.ipagination.current = 1
      this.loadData()
    },

    // 重置
    searchReset() {
      this.queryParam = {
        applicantName: '',
        reimbursementType: '',
        approvalStatus: ''
      }
      this.searchQuery()
    },

    // 表格变化
    handleTableChange(pagination) {
      this.ipagination.current = pagination.current
      this.ipagination.pageSize = pagination.pageSize
      this.loadData()
    },

    // JeecgListMixin需要的方法
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },

    // 获取报销类型颜色
    getTypeColor(type) {
      const colorMap = {
        'TRAVEL': 'blue',
        'MEAL': 'green',
        'MATERIAL': 'orange',
        'ACCOMMODATION': 'purple',
        'OTHER': 'default'
      }
      return colorMap[type] || 'default'
    },

    // 获取报销类型文本
    getTypeText(type) {
      const textMap = {
        'TRAVEL': '交通费',
        'MEAL': '餐费',
        'MATERIAL': '材料费',
        'ACCOMMODATION': '住宿费',
        'OTHER': '其他'
      }
      return textMap[type] || type
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'PENDING': 'orange',
        'APPROVED': 'green',
        'REJECTED': 'red'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'PENDING': '待审批',
        'APPROVED': '已审批',
        'REJECTED': '已拒绝'
      }
      return textMap[status] || status
    },

    // 新增报销申请
    handleAdd() {
      this.currentRecord = {}
      this.modalVisible = true
    },

    // 编辑报销申请
    handleEdit(record) {
      this.currentRecord = { ...record }
      this.modalVisible = true
    },

    // 查看报销申请
    handleView(record) {
      this.currentRecord = { ...record }
      this.modalVisible = true
    },

    // 审批报销申请
    handleApprove(record) {
      this.currentRecord = { ...record }
      this.approvalModalVisible = true
    },

    // 删除报销申请
    handleDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除申请单号 ${record.applicationNumber} 吗？`,
        onOk: () => {
          deleteReimbursement(record.id).then(res => {
            if (res.success) {
              this.$message.success('删除成功')
              this.loadData()
            } else {
              this.$message.error(res.message || '删除失败')
            }
          })
        }
      })
    },

    // 批量审批
    handleBatchApprove() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要审批的记录')
        return
      }

      this.$confirm({
        title: '批量审批',
        content: `确定要批量审批选中的 ${this.selectedRowKeys.length} 条记录吗？`,
        onOk: () => {
          approveReimbursement({
            ids: this.selectedRowKeys.join(','),
            approvalStatus: 'APPROVED',
            approvalRemark: '批量审批通过'
          }).then(res => {
            if (res.success) {
              this.$message.success('批量审批成功')
              this.selectedRowKeys = []
              this.loadData()
            } else {
              this.$message.error(res.message || '批量审批失败')
            }
          })
        }
      })
    },

    // 导出
    handleExport() {
      this.$message.info('导出功能开发中...')
    },

    // 查看凭证
    handleViewVoucher(record) {
      this.$message.info('查看凭证功能开发中...')
    },

    // 报销申请弹窗确定
    handleModalOk(formData) {
      this.modalVisible = false
      this.loadData()
    },

    // 报销申请弹窗取消
    handleModalCancel() {
      this.modalVisible = false
    },

    // 审批弹窗确定
    handleApprovalModalOk(approvalData) {
      this.approvalModalVisible = false
      this.loadData()
    },

    // 审批弹窗取消
    handleApprovalModalCancel() {
      this.approvalModalVisible = false
    }
  }
}
</script>

<style scoped>
@import '~@assets/less/common.less';
</style>
