<!-- 基于销售管理模块重构的珐琅馆排班管理页面 -->
<template>
  <a-row :gutter="24">
    <a-col :md="24">
      <a-card :style="cardStyle" :bordered="false">
        
        <!-- 视图模式切换区域 -->
        <div class="view-mode-switcher" style="margin-bottom: 16px;">
          <a-button-group>
            <a-button :type="viewMode === 'list' ? 'primary' : 'default'" @click="setViewMode('list')">
              <a-icon type="unordered-list" />
              列表视图
            </a-button>
            <a-button :type="viewMode === 'calendar' ? 'primary' : 'default'" @click="setViewMode('calendar')">
              <a-icon type="calendar" />
              日历视图
            </a-button>
            <a-button :type="viewMode === 'statistics' ? 'primary' : 'default'" @click="setViewMode('statistics')">
              <a-icon type="bar-chart" />
              统计视图
            </a-button>
          </a-button-group>
        </div>

        <!-- 查询区域 -->
        <div class="table-page-search-wrapper" v-if="viewMode === 'list'">
          <!-- 搜索区域 -->
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :md="6" :sm="24">
                <a-form-item label="员工姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入员工姓名" v-model="queryParam.employeeName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="排班日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-range-picker
                    style="width:100%"
                    v-model="queryParam.scheduleDateRange"
                    format="YYYY-MM-DD"
                    :placeholder="['开始日期', '结束日期']"
                    @change="onDateChange"
                    @ok="onDateOk"
                  />
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="班次类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-select placeholder="请选择班次类型" allow-clear v-model="queryParam.shiftType">
                    <a-select-option value="morning">早班</a-select-option>
                    <a-select-option value="afternoon">午班</a-select-option>
                    <a-select-option value="evening">晚班</a-select-option>
                    <a-select-option value="fullday">全天班</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-col :md="6" :sm="24">
                  <a-button type="primary" @click="searchQuery">查询</a-button>
                  <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
                  <a @click="handleToggleSearch" style="margin-left: 8px">
                    {{ toggleSearchStatus ? '收起' : '展开' }}
                    <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                  </a>
                </a-col>
              </span>
            </a-row>
            <template v-if="toggleSearchStatus">
              <a-row :gutter="24">
                <a-col :md="6" :sm="24">
                  <a-form-item label="工作区域" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择工作区域" showSearch allow-clear optionFilterProp="children" v-model="queryParam.workArea">
                      <a-select-option v-for="(item,index) in workAreaList" :key="index" :value="item.value">
                        {{ item.text }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="排班状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-select placeholder="请选择排班状态" allow-clear v-model="queryParam.status">
                      <a-select-option value="scheduled">已排班</a-select-option>
                      <a-select-option value="confirmed">已确认</a-select-option>
                      <a-select-option value="cancelled">已取消</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :md="6" :sm="24">
                  <a-form-item label="备注信息" :labelCol="labelCol" :wrapperCol="wrapperCol">
                    <a-input placeholder="请输入备注信息" v-model="queryParam.notes"></a-input>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator" style="margin-top: 5px" v-if="viewMode === 'list'">
          <a-button v-if="btnEnableList.indexOf(1)>-1" @click="myHandleAdd" type="primary" icon="plus">新增排班</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="calendar" @click="batchSchedule">批量排班</a-button>
          <a-button v-if="btnEnableList.indexOf(1)>-1" icon="delete" @click="batchDel">删除</a-button>
          <a-button v-if="checkFlag && btnEnableList.indexOf(2)>-1" icon="check" @click="batchSetStatus('confirmed')">批量确认</a-button>
          <a-button v-if="checkFlag && btnEnableList.indexOf(7)>-1" icon="stop" @click="batchSetStatus('cancelled')">批量取消</a-button>
          <a-button v-if="isShowExcel && btnEnableList.indexOf(3)>-1" icon="download" @click="handleExport">导出</a-button>
          
          <!-- 列设置 -->
          <a-popover trigger="click" placement="right">
            <template slot="content">
              <a-checkbox-group @change="onColChange" v-model="settingDataIndex" :defaultValue="settingDataIndex">
                <a-row style="width: 500px">
                  <template v-for="(item,index) in defColumns">
                    <template>
                      <a-col :span="8">
                        <a-checkbox :value="item.dataIndex">
                          <j-ellipsis :value="item.title" :length="10"></j-ellipsis>
                        </a-checkbox>
                      </a-col>
                    </template>
                  </template>
                </a-row>
                <a-row style="padding-top: 10px;">
                  <a-col>
                    恢复默认列配置：<a-button @click="handleRestDefault" type="link" size="small">恢复默认</a-button>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </template>
            <a-button icon="setting">列设置</a-button>
          </a-popover>
          
          <a-tooltip placement="left" title="排班管理支持多种视图模式，可以进行批量排班操作。
          勾选排班记录后可以进行批量操作（删除、确认、取消）" slot="action">
            <a-icon v-if="btnEnableList.indexOf(1)>-1" type="question-circle" style="font-size:20px;float:right;" />
          </a-tooltip>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <!-- 列表视图 -->
          <div v-if="viewMode === 'list'">
            <a-table
              ref="table"
              size="middle"
              bordered
              rowKey="id"
              :columns="columns"
              :dataSource="dataSource"
              :components="handleDrag(columns)"
              :pagination="ipagination"
              :scroll="scroll"
              :loading="loading"
              :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
              @change="handleTableChange">
              
              <!-- 操作列 -->
              <span slot="action" slot-scope="text, record">
                <a @click="myHandleDetail(record)">查看</a>
                <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
                <a v-if="btnEnableList.indexOf(1)>-1" @click="myHandleEdit(record)">编辑</a>
                <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
                <a v-if="btnEnableList.indexOf(1)>-1" @click="myHandleCopyAdd(record)">复制</a>
                <a-divider v-if="btnEnableList.indexOf(1)>-1" type="vertical" />
                <a-popconfirm v-if="btnEnableList.indexOf(1)>-1" title="确定删除吗?" @confirm="() => myHandleDelete(record)">
                  <a>删除</a>
                </a-popconfirm>
              </span>
              
              <!-- 状态列 -->
              <template slot="customRenderStatus" slot-scope="status">
                <a-tag v-if="status == 'scheduled'" color="blue">已排班</a-tag>
                <a-tag v-if="status == 'confirmed'" color="green">已确认</a-tag>
                <a-tag v-if="status == 'cancelled'" color="red">已取消</a-tag>
              </template>
              
              <!-- 员工信息列 -->
              <template slot="customRenderEmployee" slot-scope="text, record">
                <div class="employee-info">
                  <a-avatar 
                    :size="32" 
                    :src="record.employeeAvatar"
                    :style="{ backgroundColor: getEmployeeColor(record.employeeName) }">
                    {{ record.employeeName && record.employeeName.charAt(0) }}
                  </a-avatar>
                  <span style="margin-left: 8px;">{{ record.employeeName }}</span>
                </div>
              </template>
              
              <!-- 班次信息列 -->
              <template slot="customRenderShift" slot-scope="text, record">
                <div class="shift-info">
                  <div>{{ getShiftTypeName(record.shiftType) }}</div>
                  <div style="color: #666; font-size: 12px;">
                    {{ record.startTime }}-{{ record.endTime }}
                  </div>
                </div>
              </template>
            </a-table>
          </div>

          <!-- 日历视图 -->
          <schedule-calendar-view
            v-if="viewMode === 'calendar'"
            :schedule-data="dataSource"
            :loading="loading"
            @add-schedule="myHandleAdd"
            @edit-schedule="myHandleEdit"
            @delete-schedule="myHandleDelete" />

          <!-- 统计视图 -->
          <schedule-statistics-view
            v-if="viewMode === 'statistics'"
            :statistics-data="statisticsData"
            :loading="statisticsLoading" />
        </div>

        <!-- 表单模态框 -->
        <cloisonne-schedule-modal ref="modalForm" @ok="modalFormOk" @close="modalFormClose"></cloisonne-schedule-modal>
        <bill-excel-iframe ref="billExcelIframe" @ok="modalFormOk" @close="modalFormClose"></bill-excel-iframe>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
  import CloisonneScheduleModal from './modules/CloisonneScheduleModal'
  import ScheduleCalendarView from './components/ScheduleCalendarView'
  import ScheduleStatisticsView from './components/ScheduleStatisticsView'
  import BillExcelIframe from '@/components/tools/BillExcelIframe'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import { ScheduleListMixin } from './mixins/ScheduleListMixin'
  import JEllipsis from '@/components/jeecg/JEllipsis'
  import JDate from '@/components/jeecg/JDate'
  import Vue from 'vue'
  
  export default {
    name: "CloisonneScheduleList",
    mixins: [JeecgListMixin, ScheduleListMixin],
    components: {
      CloisonneScheduleModal,
      ScheduleCalendarView,
      ScheduleStatisticsView,
      BillExcelIframe,
      JEllipsis,
      JDate
    },
    data() {
      return {
        // 视图模式
        viewMode: 'list',
        
        // 查询条件
        queryParam: {
          employeeName: "",
          scheduleDateRange: [],
          shiftType: undefined,
          workArea: undefined,
          status: undefined,
          notes: ""
        },
        
        // 统计数据
        statisticsData: {},
        statisticsLoading: false,
        
        // 工作区域列表
        workAreaList: [
          { value: 'front_desk', text: '前台接待' },
          { value: 'workshop', text: '制作工坊' },
          { value: 'exhibition', text: '展览区域' },
          { value: 'cafe', text: '咖啡区域' }
        ],
        
        labelCol: {
          span: 5
        },
        wrapperCol: {
          span: 18,
          offset: 1
        },
        
        // 默认索引
        defDataIndex: ['action', 'employeeName', 'scheduleDate', 'shiftType', 'workArea', 'startTime', 'endTime', 'status', 'notes'],
        
        // 默认列
        defColumns: [
          {
            title: '操作',
            dataIndex: 'action',
            align: "center", 
            width: 180,
            scopedSlots: { customRender: 'action' },
          },
          { 
            title: '员工信息', 
            dataIndex: 'employeeName',
            width: 150,
            scopedSlots: { customRender: 'customRenderEmployee' }
          },
          { title: '排班日期', dataIndex: 'scheduleDate', width: 120 },
          { 
            title: '班次信息', 
            dataIndex: 'shiftType',
            width: 120,
            scopedSlots: { customRender: 'customRenderShift' }
          },
          { title: '工作区域', dataIndex: 'workArea', width: 100 },
          { title: '开始时间', dataIndex: 'startTime', width: 100 },
          { title: '结束时间', dataIndex: 'endTime', width: 100 },
          { 
            title: '状态', 
            dataIndex: 'status', 
            width: 80, 
            align: "center",
            scopedSlots: { customRender: 'customRenderStatus' }
          },
          { title: '备注', dataIndex: 'notes', width: 200, ellipsis: true }
        ],
        
        url: {
          list: "/cloisonne/schedule/list",
          delete: "/cloisonne/schedule/delete",
          deleteBatch: "/cloisonne/schedule/deleteBatch",
          batchSetStatusUrl: "/cloisonne/schedule/batchSetStatus"
        }
      }
    },
    
    created() {
      this.initScheduleData()
    },
    
    methods: {
      // 设置视图模式
      setViewMode(mode) {
        this.viewMode = mode
        if (mode === 'calendar') {
          this.loadCalendarData()
        } else if (mode === 'statistics') {
          this.loadStatisticsData()
        }
      },
      
      // 获取员工颜色
      getEmployeeColor(name) {
        const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae']
        const index = name ? name.charCodeAt(0) % colors.length : 0
        return colors[index]
      },
      
      // 获取班次类型名称
      getShiftTypeName(type) {
        const typeMap = {
          'morning': '早班',
          'afternoon': '午班', 
          'evening': '晚班',
          'fullday': '全天班'
        }
        return typeMap[type] || type
      },
      
      // 批量排班
      batchSchedule() {
        this.$refs.modalForm.add()
        this.$refs.modalForm.title = "批量排班"
        this.$refs.modalForm.scheduleMode = 'batch'
      },
      
      // 加载日历数据
      loadCalendarData() {
        // 日历视图数据加载逻辑
      },
      
      // 加载统计数据
      loadStatisticsData() {
        this.statisticsLoading = true
        // 统计数据加载逻辑
        setTimeout(() => {
          this.statisticsLoading = false
        }, 1000)
      }
    }
  }
</script>

<style scoped>
  @import '~@assets/less/common.less';
  
  .view-mode-switcher {
    text-align: center;
  }
  
  .employee-info {
    display: flex;
    align-items: center;
  }
  
  .shift-info {
    line-height: 1.2;
  }
</style>
