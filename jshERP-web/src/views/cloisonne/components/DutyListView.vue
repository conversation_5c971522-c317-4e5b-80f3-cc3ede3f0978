<template>
  <a-card>
    <div class="table-wrapper">
      <a-table
        :columns="columns"
        :dataSource="schedules"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: true }"
        size="middle"
        bordered
        rowKey="id"
        class="duty-table"
      >
        <!-- 状态列自定义渲染 -->
        <template slot="statusSlot" slot-scope="text">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        
        <!-- 班次类型自定义渲染 -->
        <template slot="shiftTypeSlot" slot-scope="text">
          <a-tag :color="getShiftTypeColor(text)">
            {{ text }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template slot="action" slot-scope="text, record">
          <div class="action-buttons">
            <a-button 
              type="link" 
              size="small" 
              icon="edit"
              @click="handleEdit(record)"
              v-has="'cloisonneDuty:edit'"
            >
              编辑
            </a-button>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定删除这条值班记录吗？"
              @confirm="handleDelete(record)"
              okText="确定"
              cancelText="取消"
            >
              <a-button 
                type="link" 
                size="small" 
                icon="delete"
                class="delete-btn"
                v-has="'cloisonneDuty:delete'"
              >
                删除
              </a-button>
            </a-popconfirm>
          </div>
        </template>
      </a-table>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'DutyListView',
  props: {
    schedules: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 表格列定义
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '值班日期',
          dataIndex: 'dutyDate',
          width: 120,
          align: 'center',
          sorter: (a, b) => new Date(a.dutyDate) - new Date(b.dutyDate)
        },
        {
          title: '员工姓名',
          dataIndex: 'employeeName',
          width: 120,
          align: 'center'
        },
        {
          title: '班次类型',
          dataIndex: 'shiftType',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'shiftTypeSlot' }
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          width: 100,
          align: 'center'
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          width: 100,
          align: 'center'
        },
        {
          title: '工作时长',
          dataIndex: 'workHours',
          width: 100,
          align: 'center',
          customRender: (text) => text ? `${text}小时` : '-'
        },
        {
          title: '状态',
          dataIndex: 'status',
          width: 100,
          align: 'center',
          scopedSlots: { customRender: 'statusSlot' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          width: 200,
          ellipsis: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'center',
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 分页配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100']
      }
    }
  },
  watch: {
    schedules: {
      handler(newVal) {
        this.pagination.total = newVal.length
      },
      immediate: true
    }
  },
  methods: {
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'normal': 'green',
        'leave': 'orange', 
        'swap': 'blue'
      }
      return colorMap[status] || 'default'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'normal': '正常',
        'leave': '请假',
        'swap': '调班'
      }
      return textMap[status] || status
    },
    
    // 获取班次类型颜色
    getShiftTypeColor(shiftType) {
      const colorMap = {
        '早班': 'orange',
        '晚班': 'green',
        '全天': 'blue'
      }
      return colorMap[shiftType] || 'default'
    },
    
    // 处理编辑
    handleEdit(record) {
      this.$emit('edit', record)
    },
    
    // 处理删除
    handleDelete(record) {
      this.$emit('delete', record)
    }
  }
}
</script>

<style scoped>
.table-wrapper {
  overflow-x: auto;
}

.duty-table {
  min-width: 800px;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  color: #ff4d4f;
}

.delete-btn:hover {
  color: #ff7875;
}

/* 表格行悬停效果 */
.duty-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .duty-table {
    font-size: 12px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .ant-divider {
    display: none;
  }
}
</style>
