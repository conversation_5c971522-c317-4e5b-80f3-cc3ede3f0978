<template>
  <div class="duty-week-month-container">
    <!-- 左侧周历视图 -->
    <div class="week-view-section">
      <a-card title="📅 周排班详情" :bordered="false" class="week-card">
        <template slot="extra">
          <a-button-group size="small">
            <a-button @click="previousWeek" icon="left">上周</a-button>
            <a-button @click="currentWeek">本周</a-button>
            <a-button @click="nextWeek" icon="right">下周</a-button>
          </a-button-group>
        </template>
        
        <div class="week-header">
          <h4>{{ currentWeekRange }}</h4>
        </div>
        
        <!-- 周历网格 -->
        <div class="week-grid">
          <!-- 时间轴 -->
          <div class="time-axis">
            <div class="time-slot-header">时间</div>
            <div v-for="hour in timeSlots" :key="hour" class="time-slot">
              {{ hour.toString().padStart(2, '0') }}:00
            </div>
          </div>
          
          <!-- 每天的排班 -->
          <div 
            v-for="(day, index) in currentWeekDays" 
            :key="index"
            class="day-column"
            :class="{ 'today': isToday(day.date) }"
          >
            <!-- 日期头部 -->
            <div class="day-header">
              <div class="day-name">{{ day.dayName }}</div>
              <div class="day-date">{{ day.date.getDate() }}</div>
            </div>
            
            <!-- 时间段 -->
            <div class="time-slots">
              <div 
                v-for="hour in timeSlots" 
                :key="hour"
                class="time-slot"
                @dblclick="handleAddDuty(day.date, hour)"
              >
                <!-- 显示该时间段的排班 -->
                <div 
                  v-for="duty in getDutiesForTimeSlot(day.date, hour)"
                  :key="duty.id"
                  class="duty-block"
                  :class="getDutyClass(duty)"
                  @click="handleEditDuty(duty)"
                >
                  <div class="duty-employee">{{ duty.employeeName }}</div>
                  <div class="duty-time">{{ duty.startTime }}-{{ duty.endTime }}</div>
                  <div class="duty-type">{{ duty.shiftType }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>
    
    <!-- 右侧月历视图 -->
    <div class="month-view-section">
      <a-card title="📆 月度概览" :bordered="false" class="month-card">
        <template slot="extra">
          <a-button-group size="small">
            <a-button @click="previousMonth" icon="left"></a-button>
            <a-button @click="currentMonth">今月</a-button>
            <a-button @click="nextMonth" icon="right"></a-button>
          </a-button-group>
        </template>
        
        <div class="month-header">
          <h4>{{ currentMonthYear }}</h4>
        </div>
        
        <!-- 月历网格 -->
        <div class="month-grid">
          <!-- 星期标题 -->
          <div class="month-weekdays">
            <div v-for="day in weekDays" :key="day" class="month-weekday">
              {{ day }}
            </div>
          </div>
          
          <!-- 日期网格 -->
          <div class="month-days">
            <div 
              v-for="day in monthCalendarDays" 
              :key="day.key"
              class="month-day"
              :class="{ 
                'today': isToday(day.date),
                'other-month': !day.isCurrentMonth,
                'has-duties': day.dutiesCount > 0,
                'selected-week': isInSelectedWeek(day.date)
              }"
              @click="handleMonthDayClick(day.date)"
            >
              <span class="month-day-number">{{ day.date.getDate() }}</span>
              <div v-if="day.dutiesCount > 0" class="duties-indicator">
                <div 
                  class="duties-dot"
                  :style="{ backgroundColor: getDutiesDensityColor(day.dutiesCount) }"
                >
                  {{ day.dutiesCount }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 图例 -->
        <div class="month-legend">
          <div class="legend-item">
            <span class="legend-dot" style="background: #52c41a;"></span>
            <span>轻度</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background: #faad14;"></span>
            <span>中度</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot" style="background: #f5222d;"></span>
            <span>密集</span>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DutyWeekMonthView',
  props: {
    schedules: {
      type: Array,
      default: () => []
    },
    currentDate: {
      type: Date,
      required: true
    }
  },
  data() {
    return {
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],
      timeSlots: [9, 10, 11, 12, 13, 14, 15, 16, 17, 18], // 9:00-18:00
      selectedWeekStart: null
    }
  },
  computed: {
    // 当前周的日期范围
    currentWeekRange() {
      const start = this.getWeekStart(this.selectedWeekStart || this.currentDate)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      
      return `${start.getMonth() + 1}月${start.getDate()}日 - ${end.getMonth() + 1}月${end.getDate()}日`
    },
    
    // 当前月年
    currentMonthYear() {
      return `${this.currentDate.getFullYear()}年 ${this.currentDate.getMonth() + 1}月`
    },
    
    // 当前周的天数
    currentWeekDays() {
      const start = this.getWeekStart(this.selectedWeekStart || this.currentDate)
      const days = []
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(start)
        date.setDate(start.getDate() + i)
        days.push({
          date: date,
          dayName: this.weekDays[i]
        })
      }
      
      return days
    },
    
    // 月历天数
    monthCalendarDays() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()
      
      // 获取当月天数
      const daysInMonth = new Date(year, month + 1, 0).getDate()
      
      // 获取当月第一天是星期几
      let firstDay = new Date(year, month, 1).getDay()
      firstDay = firstDay === 0 ? 6 : firstDay - 1
      
      const days = []
      
      // 添加上个月的尾部天数
      const prevMonth = new Date(year, month - 1, 0).getDate()
      for (let i = firstDay - 1; i >= 0; i--) {
        const date = new Date(year, month - 1, prevMonth - i)
        days.push({
          key: `prev-${prevMonth - i}`,
          date: date,
          isCurrentMonth: false,
          dutiesCount: this.getDayDutiesCount(date)
        })
      }
      
      // 添加当月的所有天数
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i)
        days.push({
          key: `current-${i}`,
          date: date,
          isCurrentMonth: true,
          dutiesCount: this.getDayDutiesCount(date)
        })
      }
      
      // 添加下个月的开头天数，补齐6行
      const totalCells = 42 // 6行 × 7列
      const remainingCells = totalCells - days.length
      for (let i = 1; i <= remainingCells; i++) {
        const date = new Date(year, month + 1, i)
        days.push({
          key: `next-${i}`,
          date: date,
          isCurrentMonth: false,
          dutiesCount: this.getDayDutiesCount(date)
        })
      }
      
      return days
    }
  },
  methods: {
    // 获取周的开始日期（周一）
    getWeekStart(date) {
      const d = new Date(date)
      const day = d.getDay()
      const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
      return new Date(d.setDate(diff))
    },
    
    // 判断是否为今天
    isToday(date) {
      const today = new Date()
      return date.toDateString() === today.toDateString()
    },
    
    // 判断是否在选中的周内
    isInSelectedWeek(date) {
      if (!this.selectedWeekStart) return false
      const weekStart = this.getWeekStart(this.selectedWeekStart)
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)
      
      return date >= weekStart && date <= weekEnd
    },
    
    // 格式化日期
    formatDate(date) {
      return date.toISOString().split('T')[0]
    },
    
    // 获取指定日期的值班数量
    getDayDutiesCount(date) {
      const dateStr = this.formatDate(date)
      return this.schedules.filter(duty => duty.dutyDate === dateStr).length
    },
    
    // 获取指定时间段的值班记录
    getDutiesForTimeSlot(date, hour) {
      const dateStr = this.formatDate(date)
      return this.schedules.filter(duty => {
        if (duty.dutyDate !== dateStr) return false
        
        const startHour = parseInt(duty.startTime.split(':')[0])
        const endHour = parseInt(duty.endTime.split(':')[0])
        
        return hour >= startHour && hour < endHour
      })
    },
    
    // 获取值班密度颜色
    getDutiesDensityColor(count) {
      if (count <= 2) return '#52c41a' // 绿色 - 轻度
      if (count <= 4) return '#faad14' // 橙色 - 中度
      return '#f5222d' // 红色 - 密集
    },
    
    // 获取值班块的样式类
    getDutyClass(duty) {
      return `duty-${duty.shiftType}`
    },
    
    // 周导航
    previousWeek() {
      const current = this.selectedWeekStart || this.currentDate
      const prev = new Date(current)
      prev.setDate(current.getDate() - 7)
      this.selectedWeekStart = prev
    },
    
    currentWeek() {
      this.selectedWeekStart = new Date()
    },
    
    nextWeek() {
      const current = this.selectedWeekStart || this.currentDate
      const next = new Date(current)
      next.setDate(current.getDate() + 7)
      this.selectedWeekStart = next
    },
    
    // 月导航
    previousMonth() {
      this.$emit('month-change', -1)
    },
    
    currentMonth() {
      this.$emit('month-change', 0)
    },
    
    nextMonth() {
      this.$emit('month-change', 1)
    },
    
    // 月历日期点击
    handleMonthDayClick(date) {
      this.selectedWeekStart = this.getWeekStart(date)
    },
    
    // 添加值班
    handleAddDuty(date, hour) {
      const dateStr = this.formatDate(date)
      this.$emit('add-duty', { date: dateStr, hour })
    },
    
    // 编辑值班
    handleEditDuty(duty) {
      this.$emit('edit-duty', duty)
    }
  }
}
</script>

<style scoped>
.duty-week-month-container {
  display: flex;
  gap: 16px;
  height: 600px;
}

.week-view-section {
  flex: 2;
  min-width: 0;
}

.month-view-section {
  flex: 1;
  min-width: 300px;
}

.week-card,
.month-card {
  height: 100%;
}

.week-card .ant-card-body,
.month-card .ant-card-body {
  height: calc(100% - 57px);
  padding: 16px;
  overflow: auto;
}

/* 周历样式 */
.week-header h4 {
  margin: 0 0 16px 0;
  color: #1890ff;
  text-align: center;
}

.week-grid {
  display: grid;
  grid-template-columns: 60px repeat(7, 1fr);
  gap: 1px;
  background: #e8e8e8;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.time-axis {
  background: #fafafa;
}

.time-slot-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  background: #f0f0f0;
  border-bottom: 1px solid #e8e8e8;
}

.time-slot {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.day-column {
  background: #fff;
}

.day-column.today {
  background: #e6f7ff;
}

.day-header {
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.day-name {
  font-size: 12px;
  color: #666;
}

.day-date {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.time-slots .time-slot {
  height: 40px;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
  cursor: pointer;
}

.time-slots .time-slot:hover {
  background: #f0f0f0;
}

.duty-block {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 10px;
  cursor: pointer;
  overflow: hidden;
}

.duty-早班 {
  background: #fff2e8;
  border: 1px solid #ffbb96;
  color: #d46b08;
}

.duty-晚班 {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #389e0d;
}

.duty-全天 {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #0958d9;
}

.duty-employee {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.duty-time,
.duty-type {
  font-size: 9px;
  opacity: 0.8;
}

/* 月历样式 */
.month-header h4 {
  margin: 0 0 16px 0;
  color: #1890ff;
  text-align: center;
}

.month-grid {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.month-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fafafa;
}

.month-weekday {
  padding: 8px 4px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  border-right: 1px solid #e8e8e8;
}

.month-weekday:last-child {
  border-right: none;
}

.month-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e8e8e8;
}

.month-day {
  height: 40px;
  background: #fff;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-day:hover {
  background: #f0f0f0;
}

.month-day.today {
  background: #e6f7ff;
}

.month-day.other-month {
  background: #f5f5f5;
  color: #ccc;
}

.month-day.selected-week {
  background: #fff2e8;
}

.month-day-number {
  font-size: 12px;
}

.duties-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
}

.duties-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  color: #fff;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.month-legend {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e8e8e8;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .duty-week-month-container {
    flex-direction: column;
    height: auto;
  }
  
  .week-view-section,
  .month-view-section {
    flex: none;
  }
  
  .week-card,
  .month-card {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .week-grid {
    grid-template-columns: 50px repeat(7, 1fr);
  }
  
  .time-slot-header,
  .time-slot {
    height: 30px;
  }
  
  .day-header {
    height: 40px;
  }
  
  .month-day {
    height: 30px;
  }
}
</style>
