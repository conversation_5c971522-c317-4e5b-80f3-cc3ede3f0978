<template>
  <a-card :title="statisticsTitle">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="staffWorkDays.length === 0" class="empty-container">
      <a-empty description="本月暂无员工值班记录" />
    </div>
    
    <div v-else class="statistics-container">
      <!-- 统计卡片 -->
      <a-row :gutter="16" class="statistics-cards">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总值班天数"
              :value="totalWorkDays"
              suffix="天"
              :value-style="{ color: '#3f8600' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="参与员工数"
              :value="staffWorkDays.length"
              suffix="人"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="平均工作天数"
              :value="averageWorkDays"
              suffix="天"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="最多工作天数"
              :value="maxWorkDays"
              suffix="天"
              :value-style="{ color: '#cf1322' }"
            />
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 员工工作统计列表 -->
      <div class="staff-list">
        <h3 class="list-title">员工工作统计</h3>
        <a-list
          :dataSource="staffWorkDays"
          :pagination="false"
          size="large"
        >
          <a-list-item slot="renderItem" slot-scope="item">
            <div class="staff-item">
              <div class="staff-info">
                <div class="staff-name">
                  <a-avatar :size="32" :style="{ backgroundColor: getAvatarColor(item.name) }">
                    {{ item.name.charAt(0) }}
                  </a-avatar>
                  <span class="name-text">{{ item.name }}</span>
                </div>
                <div class="work-summary">
                  <a-tag color="blue">{{ item.days }} 天</a-tag>
                  <span class="shift-details">
                    {{ formatShiftDetails(item.shifts) }}
                  </span>
                </div>
              </div>
              
              <!-- 班次分布进度条 -->
              <div class="shift-progress">
                <div class="progress-item" v-for="(count, shift) in item.shifts" :key="shift">
                  <span class="shift-label">{{ shift }}</span>
                  <a-progress
                    :percent="(count / item.days) * 100"
                    :show-info="false"
                    :stroke-color="getShiftColor(shift)"
                    size="small"
                  />
                  <span class="shift-count">{{ count }}次</span>
                </div>
              </div>
            </div>
          </a-list-item>
        </a-list>
      </div>
      
      <!-- 班次分布图表 -->
      <div class="chart-container">
        <h3 class="chart-title">班次分布统计</h3>
        <div class="shift-chart">
          <div 
            v-for="(count, shift) in shiftDistribution" 
            :key="shift"
            class="shift-bar"
          >
            <div class="shift-bar-label">{{ shift }}</div>
            <div class="shift-bar-container">
              <div 
                class="shift-bar-fill"
                :style="{ 
                  width: `${(count / maxShiftCount) * 100}%`,
                  backgroundColor: getShiftColor(shift)
                }"
              ></div>
              <span class="shift-bar-count">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script>
export default {
  name: 'DutyStatisticsView',
  props: {
    schedules: {
      type: Array,
      default: () => []
    },
    currentDate: {
      type: Date,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', 
                   '七月', '八月', '九月', '十月', '十一月', '十二月']
    }
  },
  computed: {
    // 统计标题
    statisticsTitle() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()
      return `${year}年 ${this.monthNames[month]} 员工值班统计`
    },
    
    // 员工工作天数统计
    staffWorkDays() {
      const year = this.currentDate.getFullYear()
      const month = this.currentDate.getMonth()
      const summary = {}
      
      // 统计每个员工的工作情况
      this.schedules.forEach(schedule => {
        const scheduleDate = new Date(schedule.dutyDate)
        if (scheduleDate.getFullYear() === year && scheduleDate.getMonth() === month) {
          if (!summary[schedule.employeeId]) {
            summary[schedule.employeeId] = {
              name: schedule.employeeName,
              days: 0,
              shifts: {}
            }
          }
          
          summary[schedule.employeeId].days++
          const shift = schedule.shiftType
          summary[schedule.employeeId].shifts[shift] = (summary[schedule.employeeId].shifts[shift] || 0) + 1
        }
      })
      
      // 转换为数组并按工作天数排序
      return Object.values(summary)
        .filter(s => s.days > 0)
        .sort((a, b) => b.days - a.days)
    },
    
    // 总工作天数
    totalWorkDays() {
      return this.staffWorkDays.reduce((total, staff) => total + staff.days, 0)
    },
    
    // 平均工作天数
    averageWorkDays() {
      if (this.staffWorkDays.length === 0) return 0
      return this.totalWorkDays / this.staffWorkDays.length
    },
    
    // 最多工作天数
    maxWorkDays() {
      if (this.staffWorkDays.length === 0) return 0
      return Math.max(...this.staffWorkDays.map(staff => staff.days))
    },
    
    // 班次分布统计
    shiftDistribution() {
      const distribution = {}
      this.staffWorkDays.forEach(staff => {
        Object.entries(staff.shifts).forEach(([shift, count]) => {
          distribution[shift] = (distribution[shift] || 0) + count
        })
      })
      return distribution
    },
    
    // 最大班次数量（用于图表比例）
    maxShiftCount() {
      const counts = Object.values(this.shiftDistribution)
      return counts.length > 0 ? Math.max(...counts) : 1
    }
  },
  methods: {
    // 获取头像颜色
    getAvatarColor(name) {
      const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
      const index = name.charCodeAt(0) % colors.length
      return colors[index]
    },
    
    // 格式化班次详情
    formatShiftDetails(shifts) {
      return Object.entries(shifts)
        .map(([shift, count]) => `${shift}:${count}次`)
        .join(', ')
    },
    
    // 获取班次颜色
    getShiftColor(shift) {
      const colorMap = {
        '早班': '#fa8c16',
        '晚班': '#52c41a',
        '全天': '#1890ff'
      }
      return colorMap[shift] || '#d9d9d9'
    }
  }
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.statistics-container {
  padding: 16px 0;
}

.statistics-cards {
  margin-bottom: 24px;
}

.staff-list {
  margin-bottom: 24px;
}

.list-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.staff-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.staff-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.staff-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.name-text {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.work-summary {
  display: flex;
  align-items: center;
  gap: 12px;
}

.shift-details {
  color: #8c8c8c;
  font-size: 14px;
}

.shift-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 44px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.shift-label {
  width: 40px;
  font-size: 12px;
  color: #8c8c8c;
}

.shift-count {
  width: 40px;
  font-size: 12px;
  color: #8c8c8c;
  text-align: right;
}

.chart-container {
  margin-top: 24px;
}

.chart-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.shift-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shift-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.shift-bar-label {
  width: 60px;
  font-size: 14px;
  color: #262626;
}

.shift-bar-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 24px;
  background-color: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.shift-bar-fill {
  height: 100%;
  border-radius: 12px;
  transition: width 0.3s ease;
}

.shift-bar-count {
  position: absolute;
  right: 8px;
  font-size: 12px;
  color: #262626;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-cards .ant-col {
    margin-bottom: 16px;
  }
  
  .staff-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .shift-progress {
    padding-left: 0;
  }
  
  .shift-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .shift-bar-label {
    width: auto;
  }
}
</style>
