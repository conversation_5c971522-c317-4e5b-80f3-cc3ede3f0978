<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false">
    
    <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
      
      <!-- 员工选择 -->
      <a-form-item label="员工">
        <a-select 
          placeholder="请选择员工" 
          v-decorator="['employeeId', { rules: [{ required: true, message: '请选择员工!' }] }]"
          showSearch
          optionFilterProp="children">
          <a-select-option v-for="employee in employeeList" :key="employee.id" :value="employee.id">
            {{ employee.name }}
          </a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- 排班日期 -->
      <a-form-item label="排班日期">
        <a-date-picker 
          style="width: 100%"
          placeholder="请选择排班日期"
          v-decorator="['scheduleDate', { rules: [{ required: true, message: '请选择排班日期!' }] }]"
          format="YYYY-MM-DD" />
      </a-form-item>
      
      <!-- 班次类型 -->
      <a-form-item label="班次类型">
        <a-select 
          placeholder="请选择班次类型" 
          v-decorator="['shiftType', { rules: [{ required: true, message: '请选择班次类型!' }] }]"
          @change="onShiftTypeChange">
          <a-select-option value="morning">早班 (08:00-12:00)</a-select-option>
          <a-select-option value="afternoon">午班 (12:00-18:00)</a-select-option>
          <a-select-option value="evening">晚班 (18:00-22:00)</a-select-option>
          <a-select-option value="fullday">全天班 (08:00-22:00)</a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- 工作区域 -->
      <a-form-item label="工作区域">
        <a-select 
          placeholder="请选择工作区域" 
          v-decorator="['workArea', { rules: [{ required: true, message: '请选择工作区域!' }] }]">
          <a-select-option value="front_desk">前台接待</a-select-option>
          <a-select-option value="workshop">制作工坊</a-select-option>
          <a-select-option value="exhibition">展览区域</a-select-option>
          <a-select-option value="cafe">咖啡区域</a-select-option>
        </a-select>
      </a-form-item>
      
      <!-- 开始时间 -->
      <a-form-item label="开始时间">
        <a-time-picker 
          style="width: 100%"
          placeholder="请选择开始时间"
          format="HH:mm"
          v-decorator="['startTime', { rules: [{ required: true, message: '请选择开始时间!' }] }]"
          @change="onTimeChange" />
      </a-form-item>
      
      <!-- 结束时间 -->
      <a-form-item label="结束时间">
        <a-time-picker 
          style="width: 100%"
          placeholder="请选择结束时间"
          format="HH:mm"
          v-decorator="['endTime', { rules: [{ required: true, message: '请选择结束时间!' }] }]"
          @change="onTimeChange" />
      </a-form-item>
      
      <!-- 工时显示 -->
      <a-form-item label="工时">
        <a-input 
          placeholder="自动计算"
          v-decorator="['workHours']"
          :readOnly="true" />
      </a-form-item>
      
      <!-- 备注 -->
      <a-form-item label="备注">
        <a-textarea 
          :rows="3"
          placeholder="请输入备注信息"
          v-decorator="['notes']" />
      </a-form-item>
      
    </a-form>
  </a-modal>
</template><script>
import moment from 'moment'

export default {
  name: "CloisonneScheduleModal",
  data() {
    return {
      title: "新增排班",
      visible: false,
      confirmLoading: false,
      
      // 表单对象
      form: this.$form.createForm(this),
      
      // 表单布局
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      
      // 当前编辑的记录
      model: {},
      
      // 员工列表（模拟数据）
      employeeList: [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' },
        { id: 3, name: '王五' },
        { id: 4, name: '赵六' },
        { id: 5, name: '孙七' }
      ],
      
      // 班次时间配置
      shiftTimeConfig: {
        'morning': { start: '08:00', end: '12:00' },
        'afternoon': { start: '12:00', end: '18:00' },
        'evening': { start: '18:00', end: '22:00' },
        'fullday': { start: '08:00', end: '22:00' }
      }
    }
  },
  
  methods: {
    /**
     * 新增
     */
    add() {
      this.model = {}
      this.title = "新增排班"
      this.visible = true
      this.form.resetFields()
    },
    
    /**
     * 编辑
     */
    edit(record) {
      this.model = Object.assign({}, record)
      this.title = "编辑排班"
      this.visible = true
      this.form.resetFields()
      
      // 设置表单值
      this.$nextTick(() => {
        this.form.setFieldsValue({
          employeeId: record.employeeId,
          scheduleDate: record.scheduleDate ? moment(record.scheduleDate) : null,
          shiftType: record.shiftType,
          workArea: record.workArea,
          startTime: record.startTime ? moment(record.startTime, 'HH:mm') : null,
          endTime: record.endTime ? moment(record.endTime, 'HH:mm') : null,
          workHours: record.workHours,
          notes: record.notes
        })
      })
    },
    
    /**
     * 查看详情
     */
    detail(record) {
      this.edit(record)
      this.title = "排班详情"
      // 可以在这里设置表单为只读状态
    },
    
    /**
     * 确定按钮
     */
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true
          
          // 格式化数据
          const formData = {
            ...values,
            scheduleDate: values.scheduleDate ? values.scheduleDate.format('YYYY-MM-DD') : null,
            startTime: values.startTime ? values.startTime.format('HH:mm') : null,
            endTime: values.endTime ? values.endTime.format('HH:mm') : null
          }
          
          // 模拟保存操作
          setTimeout(() => {
            this.confirmLoading = false
            this.$message.success('保存成功')
            this.handleCancel()
            this.$emit('ok')
          }, 1000)
        }
      })
    },
    
    /**
     * 取消按钮
     */
    handleCancel() {
      this.visible = false
      this.form.resetFields()
      this.model = {}
      this.$emit('close')
    },
    
    /**
     * 班次类型变化
     */
    onShiftTypeChange(value) {
      const config = this.shiftTimeConfig[value]
      if (config) {
        this.form.setFieldsValue({
          startTime: moment(config.start, 'HH:mm'),
          endTime: moment(config.end, 'HH:mm')
        })
        this.calculateWorkHours()
      }
    },
    
    /**
     * 时间变化
     */
    onTimeChange() {
      this.$nextTick(() => {
        this.calculateWorkHours()
      })
    },
    
    /**
     * 计算工时
     */
    calculateWorkHours() {
      const startTime = this.form.getFieldValue('startTime')
      const endTime = this.form.getFieldValue('endTime')
      
      if (startTime && endTime) {
        const start = moment(startTime)
        const end = moment(endTime)
        const hours = end.diff(start, 'hours', true)
        this.form.setFieldsValue({ workHours: hours.toFixed(1) + '小时' })
      }
    }
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>