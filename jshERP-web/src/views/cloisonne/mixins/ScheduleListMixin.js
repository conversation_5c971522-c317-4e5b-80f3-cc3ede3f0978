/**
 * 排班管理列表页面业务逻辑Mixin
 * 基于销售管理模块的BillListMixin，适配排班管理业务需求
 */
import { getScheduleList, getScheduleStatistics, getEmployees, batchDeleteSchedule, batchUpdateScheduleStatus } from '@/api/cloisonne/schedule'
import { filterObj } from '@/utils/util'
import dayjs from 'dayjs'

export const ScheduleListMixin = {
  data() {
    return {
      // 排班特有的查询参数
      employeeList: [],
      shiftTypeList: [
        { value: 'morning', text: '早班' },
        { value: 'afternoon', text: '午班' },
        { value: 'evening', text: '晚班' },
        { value: 'fullday', text: '全天班' }
      ],
      workAreaList: [
        { value: 'front_desk', text: '前台接待' },
        { value: 'workshop', text: '制作工坊' },
        { value: 'exhibition', text: '展览区域' },
        { value: 'cafe', text: '咖啡区域' }
      ],
      
      // 视图模式控制
      viewMode: 'list',
      
      // 日历相关数据
      calendarData: {},
      currentDate: dayjs(),
      
      // 统计数据
      statisticsData: {},
      statisticsLoading: false,
      
      // 批量操作状态
      batchOperationLoading: false,

      // JeecgListMixin兼容属性
      checkFlag: false,
      isShowExcel: true
    }
  },
  
  created() {
    this.initScheduleData()
  },
  
  methods: {
    /**
     * 初始化排班相关数据
     */
    initScheduleData() {
      this.getEmployeeList()
      this.getShiftTypeList()
      this.getWorkAreaList()
    },
    
    /**
     * 获取员工列表
     */
    getEmployeeList() {
      getEmployees().then(res => {
        if (res.success) {
          this.employeeList = res.result || []
        } else {
          this.$message.error('获取员工列表失败：' + res.message)
        }
      }).catch(err => {
        console.error('获取员工列表异常：', err)
        this.$message.error('获取员工列表异常')
      })
    },
    
    /**
     * 获取班次类型列表
     */
    getShiftTypeList() {
      // 班次类型是固定的，可以从配置中获取
      // 这里使用默认配置
    },
    
    /**
     * 获取工作区域列表
     */
    getWorkAreaList() {
      // 工作区域是固定的，可以从配置中获取
      // 这里使用默认配置
    },
    
    /**
     * 视图模式切换
     */
    setViewMode(mode) {
      this.viewMode = mode
      if (mode === 'calendar') {
        this.loadCalendarData()
      } else if (mode === 'statistics') {
        this.loadStatisticsData()
      } else if (mode === 'list') {
        this.loadData(1)
      }
    },
    
    /**
     * 加载日历数据
     */
    loadCalendarData() {
      this.loading = true
      const params = this.getQueryParams()
      
      getScheduleList(params).then(res => {
        this.loading = false
        if (res.success) {
          // 将列表数据转换为日历格式
          this.calendarData = this.convertToCalendarData(res.result.records || [])
        } else {
          this.$message.error('加载日历数据失败：' + res.message)
        }
      }).catch(err => {
        this.loading = false
        console.error('加载日历数据异常：', err)
        this.$message.error('加载日历数据异常')
      })
    },
    
    /**
     * 加载统计数据
     */
    loadStatisticsData() {
      this.statisticsLoading = true
      const params = this.getQueryParams()
      
      getScheduleStatistics(params).then(res => {
        this.statisticsLoading = false
        if (res.success) {
          this.statisticsData = res.result || {}
        } else {
          this.$message.error('加载统计数据失败：' + res.message)
        }
      }).catch(err => {
        this.statisticsLoading = false
        console.error('加载统计数据异常：', err)
        this.$message.error('加载统计数据异常')
      })
    },
    
    /**
     * 将列表数据转换为日历格式
     */
    convertToCalendarData(scheduleList) {
      const calendarData = {}
      
      scheduleList.forEach(schedule => {
        const date = schedule.scheduleDate
        if (!calendarData[date]) {
          calendarData[date] = []
        }
        calendarData[date].push(schedule)
      })
      
      return calendarData
    },
    
    /**
     * 获取查询参数
     */
    getQueryParams() {
      let param = Object.assign({}, this.queryParam, this.isorter, this.filters)
      param.current = this.ipagination.current
      param.size = this.ipagination.pageSize
      
      // 处理日期范围
      if (param.scheduleDateRange && param.scheduleDateRange.length === 2) {
        param.startDate = dayjs(param.scheduleDateRange[0]).format('YYYY-MM-DD')
        param.endDate = dayjs(param.scheduleDateRange[1]).format('YYYY-MM-DD')
        delete param.scheduleDateRange
      }
      
      return filterObj(param)
    },
    
    /**
     * 排班冲突检测
     */
    checkScheduleConflict(scheduleData) {
      // 检测同一员工同一时间是否有冲突
      const { employeeId, scheduleDate, startTime, endTime } = scheduleData
      
      // 这里应该调用后端API进行冲突检测
      // 暂时返回模拟结果
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟冲突检测结果
          const hasConflict = false
          resolve({
            hasConflict,
            conflictInfo: hasConflict ? '该员工在此时间段已有排班' : null
          })
        }, 500)
      })
    },
    
    /**
     * 批量排班操作
     */
    batchScheduleOperation(operation, selectedRows) {
      if (!selectedRows || selectedRows.length === 0) {
        this.$message.warning('请选择要操作的排班记录')
        return
      }
      
      this.batchOperationLoading = true
      const ids = selectedRows.map(row => row.id)
      
      let promise
      switch (operation) {
        case 'delete':
          promise = batchDeleteSchedule(ids)
          break
        case 'confirm':
          promise = batchUpdateScheduleStatus({ ids, status: 'confirmed' })
          break
        case 'cancel':
          promise = batchUpdateScheduleStatus({ ids, status: 'cancelled' })
          break
        default:
          this.$message.error('未知的批量操作类型')
          this.batchOperationLoading = false
          return
      }
      
      promise.then(res => {
        this.batchOperationLoading = false
        if (res.success) {
          this.$message.success(`批量${this.getOperationName(operation)}成功`)
          this.loadData()
          this.onClearSelected()
        } else {
          this.$message.error(`批量${this.getOperationName(operation)}失败：` + res.message)
        }
      }).catch(err => {
        this.batchOperationLoading = false
        console.error(`批量${this.getOperationName(operation)}异常：`, err)
        this.$message.error(`批量${this.getOperationName(operation)}异常`)
      })
    },
    
    /**
     * 获取操作名称
     */
    getOperationName(operation) {
      const names = {
        'delete': '删除',
        'confirm': '确认',
        'cancel': '取消'
      }
      return names[operation] || operation
    },
    
    /**
     * 批量删除
     */
    batchDel() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      }
      
      const that = this
      this.$confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedRowKeys.length} 条排班记录吗？`,
        onOk() {
          const selectedRows = that.selectedRows
          that.batchScheduleOperation('delete', selectedRows)
        }
      })
    },
    
    /**
     * 批量设置状态
     */
    batchSetStatus(status) {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      }
      
      const statusName = status === 'confirmed' ? '确认' : '取消'
      const that = this
      this.$confirm({
        title: `批量${statusName}`,
        content: `确定要${statusName}选中的 ${this.selectedRowKeys.length} 条排班记录吗？`,
        onOk() {
          const selectedRows = that.selectedRows
          that.batchScheduleOperation(status === 'confirmed' ? 'confirm' : 'cancel', selectedRows)
        }
      })
    },
    
    /**
     * 日期变化处理
     */
    onDateChange(date, dateString) {
      // 处理日期范围变化
    },
    
    /**
     * 日期确认处理
     */
    onDateOk(value) {
      // 处理日期确认
    },
    
    /**
     * 搜索重置
     */
    searchReset() {
      this.queryParam = {
        employeeName: "",
        scheduleDateRange: [],
        shiftType: undefined,
        workArea: undefined,
        status: undefined,
        notes: ""
      }
      this.loadData(1)
    },
    
    /**
     * 导出Excel
     */
    handleExport() {
      const params = this.getQueryParams()
      this.$message.info('导出功能开发中...')
      // 这里应该调用导出API
    },
    
    /**
     * 获取班次类型名称
     */
    getShiftTypeName(type) {
      const typeMap = {
        'morning': '早班',
        'afternoon': '午班',
        'evening': '晚班',
        'fullday': '全天班'
      }
      return typeMap[type] || type
    },
    
    /**
     * 获取工作区域名称
     */
    getWorkAreaName(area) {
      const areaMap = {
        'front_desk': '前台接待',
        'workshop': '制作工坊',
        'exhibition': '展览区域',
        'cafe': '咖啡区域'
      }
      return areaMap[area] || area
    },
    
    /**
     * 获取状态名称
     */
    getStatusName(status) {
      const statusMap = {
        'scheduled': '已排班',
        'confirmed': '已确认',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },

    /**
     * 新增排班 - JeecgListMixin兼容方法
     */
    myHandleAdd() {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = "新增排班"
      this.$refs.modalForm.scheduleMode = 'single'
    },

    /**
     * 编辑排班 - JeecgListMixin兼容方法
     */
    myHandleEdit(record) {
      this.$refs.modalForm.edit(record)
      this.$refs.modalForm.title = "编辑排班"
      this.$refs.modalForm.scheduleMode = 'single'
    },

    /**
     * 查看排班详情 - JeecgListMixin兼容方法
     */
    myHandleDetail(record) {
      this.$refs.modalForm.detail(record)
      this.$refs.modalForm.title = "排班详情"
    },

    /**
     * 复制新增排班 - JeecgListMixin兼容方法
     */
    myHandleCopyAdd(record) {
      this.$refs.modalForm.add()
      this.$refs.modalForm.title = "复制新增排班"
      this.$refs.modalForm.scheduleMode = 'single'
      // 复制数据逻辑
      this.$nextTick(() => {
        this.$refs.modalForm.form.setFieldsValue({
          employeeId: record.employeeId,
          shiftType: record.shiftType,
          workArea: record.workArea,
          startTime: record.startTime,
          endTime: record.endTime,
          notes: record.notes
        })
      })
    },

    /**
     * 删除排班 - JeecgListMixin兼容方法
     */
    myHandleDelete(record) {
      const that = this
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这条排班记录吗？',
        onOk() {
          // 调用删除API
          that.$message.success('删除成功')
          that.loadData()
        }
      })
    }
  }
}
