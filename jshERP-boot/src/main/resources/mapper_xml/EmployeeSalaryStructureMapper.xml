<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.EmployeeSalaryStructureMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.EmployeeSalaryStructure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="element_id" jdbcType="BIGINT" property="elementId" />
    <result column="element_code" jdbcType="VARCHAR" property="elementCode" />
    <result column="element_name" jdbcType="VARCHAR" property="elementName" />
    <result column="amount_or_rate" jdbcType="DECIMAL" property="amountOrRate" />
    <result column="effective_date" jdbcType="DATE" property="effectiveDate" />
    <result column="expiry_date" jdbcType="DATE" property="expiryDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    id, employee_id, employee_name, element_id, element_code, element_name, amount_or_rate, 
    effective_date, expiry_date, status, remark, tenant_id, delete_flag, create_time, 
    creator, update_time, updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_employee_salary_structure
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEmployeeId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_employee_salary_structure
    where employee_id = #{employeeId,jdbcType=BIGINT}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
      and status = '0'
      and (expiry_date IS NULL OR expiry_date >= CURDATE())
    order by element_id
  </select>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.EmployeeSalaryStructure">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_employee_salary_structure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="elementId != null">
        element_id,
      </if>
      <if test="elementCode != null">
        element_code,
      </if>
      <if test="elementName != null">
        element_name,
      </if>
      <if test="amountOrRate != null">
        amount_or_rate,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="elementId != null">
        #{elementId,jdbcType=BIGINT},
      </if>
      <if test="elementCode != null">
        #{elementCode,jdbcType=VARCHAR},
      </if>
      <if test="elementName != null">
        #{elementName,jdbcType=VARCHAR},
      </if>
      <if test="amountOrRate != null">
        #{amountOrRate,jdbcType=DECIMAL},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=DATE},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

</mapper>
