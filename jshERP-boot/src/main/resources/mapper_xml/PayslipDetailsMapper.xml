<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.PayslipDetailsMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.PayslipDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payslip_id" jdbcType="BIGINT" property="payslipId" />
    <result column="element_id" jdbcType="BIGINT" property="elementId" />
    <result column="element_code" jdbcType="VARCHAR" property="elementCode" />
    <result column="element_name" jdbcType="VARCHAR" property="elementName" />
    <result column="element_type" jdbcType="VARCHAR" property="elementType" />
    <result column="calculation_base" jdbcType="DECIMAL" property="calculationBase" />
    <result column="calculation_rate" jdbcType="DECIMAL" property="calculationRate" />
    <result column="calculated_amount" jdbcType="DECIMAL" property="calculatedAmount" />
    <result column="calculation_formula" jdbcType="VARCHAR" property="calculationFormula" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    id, payslip_id, element_id, element_code, element_name, element_type, calculation_base, 
    calculation_rate, calculated_amount, calculation_formula, remark, tenant_id, delete_flag, 
    create_time, creator, update_time, updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payslip_details
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByPayslipId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payslip_details
    where payslip_id = #{payslipId,jdbcType=BIGINT}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
    order by element_id
  </select>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.PayslipDetails">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_payslip_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payslipId != null">
        payslip_id,
      </if>
      <if test="elementId != null">
        element_id,
      </if>
      <if test="elementCode != null">
        element_code,
      </if>
      <if test="elementName != null">
        element_name,
      </if>
      <if test="elementType != null">
        element_type,
      </if>
      <if test="calculationBase != null">
        calculation_base,
      </if>
      <if test="calculationRate != null">
        calculation_rate,
      </if>
      <if test="calculatedAmount != null">
        calculated_amount,
      </if>
      <if test="calculationFormula != null">
        calculation_formula,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payslipId != null">
        #{payslipId,jdbcType=BIGINT},
      </if>
      <if test="elementId != null">
        #{elementId,jdbcType=BIGINT},
      </if>
      <if test="elementCode != null">
        #{elementCode,jdbcType=VARCHAR},
      </if>
      <if test="elementName != null">
        #{elementName,jdbcType=VARCHAR},
      </if>
      <if test="elementType != null">
        #{elementType,jdbcType=VARCHAR},
      </if>
      <if test="calculationBase != null">
        #{calculationBase,jdbcType=DECIMAL},
      </if>
      <if test="calculationRate != null">
        #{calculationRate,jdbcType=DECIMAL},
      </if>
      <if test="calculatedAmount != null">
        #{calculatedAmount,jdbcType=DECIMAL},
      </if>
      <if test="calculationFormula != null">
        #{calculationFormula,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

</mapper>
