<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.PayrollElementMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.PayrollElement">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="element_code" jdbcType="VARCHAR" property="elementCode" />
    <result column="element_name" jdbcType="VARCHAR" property="elementName" />
    <result column="element_type" jdbcType="VARCHAR" property="elementType" />
    <result column="calculation_rule" jdbcType="VARCHAR" property="calculationRule" />
    <result column="formula_expression" jdbcType="VARCHAR" property="formulaExpression" />
    <result column="tax_attribute" jdbcType="VARCHAR" property="taxAttribute" />
    <result column="is_mandatory" jdbcType="VARCHAR" property="isMandatory" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    id, element_code, element_name, element_type, calculation_rule, formula_expression, 
    tax_attribute, is_mandatory, display_order, status, remark, tenant_id, delete_flag, 
    create_time, creator, update_time, updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payroll_elements
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByElementCode" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payroll_elements
    where element_code = #{elementCode,jdbcType=VARCHAR}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>

  <select id="selectAllActive" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payroll_elements
    where tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
      and status = '0'
    order by display_order
  </select>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.PayrollElement">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_payroll_elements
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="elementCode != null">
        element_code,
      </if>
      <if test="elementName != null">
        element_name,
      </if>
      <if test="elementType != null">
        element_type,
      </if>
      <if test="calculationRule != null">
        calculation_rule,
      </if>
      <if test="formulaExpression != null">
        formula_expression,
      </if>
      <if test="taxAttribute != null">
        tax_attribute,
      </if>
      <if test="isMandatory != null">
        is_mandatory,
      </if>
      <if test="displayOrder != null">
        display_order,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="elementCode != null">
        #{elementCode,jdbcType=VARCHAR},
      </if>
      <if test="elementName != null">
        #{elementName,jdbcType=VARCHAR},
      </if>
      <if test="elementType != null">
        #{elementType,jdbcType=VARCHAR},
      </if>
      <if test="calculationRule != null">
        #{calculationRule,jdbcType=VARCHAR},
      </if>
      <if test="formulaExpression != null">
        #{formulaExpression,jdbcType=VARCHAR},
      </if>
      <if test="taxAttribute != null">
        #{taxAttribute,jdbcType=VARCHAR},
      </if>
      <if test="isMandatory != null">
        #{isMandatory,jdbcType=VARCHAR},
      </if>
      <if test="displayOrder != null">
        #{displayOrder,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

</mapper>
