<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.SalaryProfileMapperEx">

    <!-- 根据条件查询薪酬档案列表 -->
    <select id="selectByCondition" resultType="com.jsh.erp.datasource.entities.SalaryProfile">
        SELECT 
            id,
            employee_id,
            employee_name,
            id_card,
            phone,
            email,
            department,
            position,
            entry_date,
            daily_wage,
            salary_status,
            remark,
            tenant_id,
            delete_flag,
            create_time,
            update_time,
            creator,
            updater
        FROM jsh_salary_profile
        WHERE tenant_id = #{tenantId}
        AND delete_flag = '0'
        <if test="employeeName != null and employeeName != ''">
            AND employee_name LIKE CONCAT('%', #{employeeName}, '%')
        </if>
        <if test="department != null and department != ''">
            AND department = #{department}
        </if>
        <if test="position != null and position != ''">
            AND position LIKE CONCAT('%', #{position}, '%')
        </if>
        <if test="salaryStatus != null and salaryStatus != ''">
            AND salary_status = #{salaryStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据员工ID查询薪酬档案 -->
    <select id="selectByEmployeeId" resultType="com.jsh.erp.datasource.entities.SalaryProfile">
        SELECT 
            id,
            employee_id,
            employee_name,
            id_card,
            phone,
            email,
            department,
            position,
            entry_date,
            daily_wage,
            salary_status,
            remark,
            tenant_id,
            delete_flag,
            create_time,
            update_time,
            creator,
            updater
        FROM jsh_salary_profile
        WHERE employee_id = #{employeeId}
        AND tenant_id = #{tenantId}
        AND delete_flag = '0'
        LIMIT 1
    </select>

    <!-- 批量查询员工薪酬档案 -->
    <select id="selectByEmployeeIds" resultType="com.jsh.erp.datasource.entities.SalaryProfile">
        SELECT 
            id,
            employee_id,
            employee_name,
            id_card,
            phone,
            email,
            department,
            position,
            entry_date,
            daily_wage,
            salary_status,
            remark,
            tenant_id,
            delete_flag,
            create_time,
            update_time,
            creator,
            updater
        FROM jsh_salary_profile
        WHERE tenant_id = #{tenantId}
        AND delete_flag = '0'
        AND employee_id IN
        <foreach collection="employeeIds" item="employeeId" open="(" separator="," close=")">
            #{employeeId}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <!-- 统计部门薪酬档案数量 -->
    <select id="countByDepartment" resultType="java.util.Map">
        SELECT 
            department,
            COUNT(*) as count,
            AVG(daily_wage) as avgDailyWage,
            SUM(daily_wage) as totalDailyWage
        FROM jsh_salary_profile
        WHERE tenant_id = #{tenantId}
        AND delete_flag = '0'
        AND salary_status = 'ACTIVE'
        GROUP BY department
        ORDER BY count DESC
    </select>

    <!-- 查询即将到期的薪酬档案（用于提醒） -->
    <select id="selectExpiringProfiles" resultType="com.jsh.erp.datasource.entities.SalaryProfile">
        SELECT 
            id,
            employee_id,
            employee_name,
            id_card,
            phone,
            email,
            department,
            position,
            entry_date,
            daily_wage,
            salary_status,
            remark,
            tenant_id,
            delete_flag,
            create_time,
            update_time,
            creator,
            updater
        FROM jsh_salary_profile
        WHERE tenant_id = #{tenantId}
        AND delete_flag = '0'
        AND salary_status = 'ACTIVE'
        AND DATEDIFF(DATE_ADD(entry_date, INTERVAL 1 YEAR), CURDATE()) &lt;= #{days}
        ORDER BY entry_date ASC
    </select>

</mapper>
