<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.PayslipMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Payslip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payslip_number" jdbcType="VARCHAR" property="payslipNumber" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="pay_period" jdbcType="VARCHAR" property="payPeriod" />
    <result column="gross_salary" jdbcType="DECIMAL" property="grossSalary" />
    <result column="net_salary" jdbcType="DECIMAL" property="netSalary" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
  </resultMap>

  <sql id="Base_Column_List">
    id, payslip_number, employee_id, employee_name, pay_period, gross_salary,
    net_salary, status, tenant_id, delete_flag, create_time, creator
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jsh_payslip
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEmployeeAndPeriod" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from jsh_payslip
    where employee_id = #{employeeId,jdbcType=BIGINT}
      and pay_period = #{payPeriod,jdbcType=VARCHAR}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>



  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Payslip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_payslip (payslip_number, employee_id, employee_name, pay_period, gross_salary, net_salary, status, tenant_id, delete_flag, creator, create_time)
    values (#{payslipNumber}, #{employeeId}, #{employeeName}, #{payPeriod}, #{grossSalary}, #{netSalary}, #{status}, #{tenantId}, #{deleteFlag}, #{creator}, #{createTime})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Payslip">
    update jsh_payslip set gross_salary = #{grossSalary}, net_salary = #{netSalary}, status = #{status}
    where id = #{id}
  </update>

</mapper>
