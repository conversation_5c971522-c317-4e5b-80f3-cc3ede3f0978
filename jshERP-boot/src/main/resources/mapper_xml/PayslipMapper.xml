<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.PayslipMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.Payslip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="payslip_number" jdbcType="VARCHAR" property="payslipNumber" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="pay_period" jdbcType="VARCHAR" property="payPeriod" />
    <result column="pay_period_start" jdbcType="DATE" property="payPeriodStart" />
    <result column="pay_period_end" jdbcType="DATE" property="payPeriodEnd" />
    <result column="gross_salary" jdbcType="DECIMAL" property="grossSalary" />
    <result column="total_deductions" jdbcType="DECIMAL" property="totalDeductions" />
    <result column="income_tax" jdbcType="DECIMAL" property="incomeTax" />
    <result column="net_salary" jdbcType="DECIMAL" property="netSalary" />
    <result column="work_days" jdbcType="DECIMAL" property="workDays" />
    <result column="overtime_hours" jdbcType="DECIMAL" property="overtimeHours" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="generated_date" jdbcType="TIMESTAMP" property="generatedDate" />
    <result column="confirmed_date" jdbcType="TIMESTAMP" property="confirmedDate" />
    <result column="paid_date" jdbcType="TIMESTAMP" property="paidDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    id, payslip_number, employee_id, employee_name, pay_period, pay_period_start, 
    pay_period_end, gross_salary, total_deductions, income_tax, net_salary, work_days, 
    overtime_hours, status, generated_date, confirmed_date, paid_date, remark, tenant_id, 
    delete_flag, create_time, creator, update_time, updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payslip
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEmployeeAndPeriod" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_payslip
    where employee_id = #{employeeId,jdbcType=BIGINT}
      and pay_period = #{payPeriod,jdbcType=VARCHAR}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from jsh_payslip
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.jsh.erp.datasource.entities.Payslip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_payslip (payslip_number, employee_id, employee_name, 
      pay_period, pay_period_start, pay_period_end, 
      gross_salary, total_deductions, income_tax, 
      net_salary, work_days, overtime_hours, 
      status, generated_date, confirmed_date, 
      paid_date, remark, tenant_id, 
      delete_flag, create_time, creator, 
      update_time, updater)
    values (#{payslipNumber,jdbcType=VARCHAR}, #{employeeId,jdbcType=BIGINT}, #{employeeName,jdbcType=VARCHAR}, 
      #{payPeriod,jdbcType=VARCHAR}, #{payPeriodStart,jdbcType=DATE}, #{payPeriodEnd,jdbcType=DATE}, 
      #{grossSalary,jdbcType=DECIMAL}, #{totalDeductions,jdbcType=DECIMAL}, #{incomeTax,jdbcType=DECIMAL}, 
      #{netSalary,jdbcType=DECIMAL}, #{workDays,jdbcType=DECIMAL}, #{overtimeHours,jdbcType=DECIMAL}, 
      #{status,jdbcType=VARCHAR}, #{generatedDate,jdbcType=TIMESTAMP}, #{confirmedDate,jdbcType=TIMESTAMP}, 
      #{paidDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, 
      #{deleteFlag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=BIGINT})
  </insert>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.Payslip">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_payslip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payslipNumber != null">
        payslip_number,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="payPeriod != null">
        pay_period,
      </if>
      <if test="payPeriodStart != null">
        pay_period_start,
      </if>
      <if test="payPeriodEnd != null">
        pay_period_end,
      </if>
      <if test="grossSalary != null">
        gross_salary,
      </if>
      <if test="totalDeductions != null">
        total_deductions,
      </if>
      <if test="incomeTax != null">
        income_tax,
      </if>
      <if test="netSalary != null">
        net_salary,
      </if>
      <if test="workDays != null">
        work_days,
      </if>
      <if test="overtimeHours != null">
        overtime_hours,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="generatedDate != null">
        generated_date,
      </if>
      <if test="confirmedDate != null">
        confirmed_date,
      </if>
      <if test="paidDate != null">
        paid_date,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payslipNumber != null">
        #{payslipNumber,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="payPeriod != null">
        #{payPeriod,jdbcType=VARCHAR},
      </if>
      <if test="payPeriodStart != null">
        #{payPeriodStart,jdbcType=DATE},
      </if>
      <if test="payPeriodEnd != null">
        #{payPeriodEnd,jdbcType=DATE},
      </if>
      <if test="grossSalary != null">
        #{grossSalary,jdbcType=DECIMAL},
      </if>
      <if test="totalDeductions != null">
        #{totalDeductions,jdbcType=DECIMAL},
      </if>
      <if test="incomeTax != null">
        #{incomeTax,jdbcType=DECIMAL},
      </if>
      <if test="netSalary != null">
        #{netSalary,jdbcType=DECIMAL},
      </if>
      <if test="workDays != null">
        #{workDays,jdbcType=DECIMAL},
      </if>
      <if test="overtimeHours != null">
        #{overtimeHours,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="generatedDate != null">
        #{generatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmedDate != null">
        #{confirmedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paidDate != null">
        #{paidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.jsh.erp.datasource.entities.Payslip">
    update jsh_payslip
    <set>
      <if test="payslipNumber != null">
        payslip_number = #{payslipNumber,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="employeeName != null">
        employee_name = #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="payPeriod != null">
        pay_period = #{payPeriod,jdbcType=VARCHAR},
      </if>
      <if test="payPeriodStart != null">
        pay_period_start = #{payPeriodStart,jdbcType=DATE},
      </if>
      <if test="payPeriodEnd != null">
        pay_period_end = #{payPeriodEnd,jdbcType=DATE},
      </if>
      <if test="grossSalary != null">
        gross_salary = #{grossSalary,jdbcType=DECIMAL},
      </if>
      <if test="totalDeductions != null">
        total_deductions = #{totalDeductions,jdbcType=DECIMAL},
      </if>
      <if test="incomeTax != null">
        income_tax = #{incomeTax,jdbcType=DECIMAL},
      </if>
      <if test="netSalary != null">
        net_salary = #{netSalary,jdbcType=DECIMAL},
      </if>
      <if test="workDays != null">
        work_days = #{workDays,jdbcType=DECIMAL},
      </if>
      <if test="overtimeHours != null">
        overtime_hours = #{overtimeHours,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="generatedDate != null">
        generated_date = #{generatedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmedDate != null">
        confirmed_date = #{confirmedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="paidDate != null">
        paid_date = #{paidDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
