<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.ReimbursementMapperEx">

    <!-- 根据条件查询报销申请列表 -->
    <select id="selectByCondition" resultType="com.jsh.erp.datasource.entities.Reimbursement">
        SELECT r.*, u.username as employee_name
        FROM jsh_reimbursement r
        LEFT JOIN jsh_user u ON r.employee_id = u.id
        WHERE r.delete_flag = '0' AND r.tenant_id = #{tenantId}
        <if test="employeeName != null and employeeName != ''">
            AND u.username LIKE CONCAT('%', #{employeeName}, '%')
        </if>
        <if test="department != null and department != ''">
            AND r.department = #{department}
        </if>
        <if test="reimbursementType != null and reimbursementType != ''">
            AND r.reimbursement_type = #{reimbursementType}
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="applyDateStart != null and applyDateStart != ''">
            AND r.apply_date >= #{applyDateStart}
        </if>
        <if test="applyDateEnd != null and applyDateEnd != ''">
            AND r.apply_date &lt;= #{applyDateEnd}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 获取报销申请详情（包含明细） -->
    <select id="selectWithItems" resultType="com.jsh.erp.datasource.entities.Reimbursement">
        SELECT r.*, u.username as employee_name
        FROM jsh_reimbursement r
        LEFT JOIN jsh_user u ON r.employee_id = u.id
        WHERE r.id = #{id} AND r.tenant_id = #{tenantId} AND r.delete_flag = '0'
    </select>

    <!-- 获取我的报销申请列表 -->
    <select id="selectMyReimbursements" resultType="com.jsh.erp.datasource.entities.Reimbursement">
        SELECT r.*
        FROM jsh_reimbursement r
        WHERE r.employee_id = #{employeeId} AND r.tenant_id = #{tenantId} AND r.delete_flag = '0'
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="applyDateStart != null and applyDateStart != ''">
            AND r.apply_date >= #{applyDateStart}
        </if>
        <if test="applyDateEnd != null and applyDateEnd != ''">
            AND r.apply_date &lt;= #{applyDateEnd}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 获取待我审批的报销申请列表 -->
    <select id="selectPendingApprovals" resultType="com.jsh.erp.datasource.entities.Reimbursement">
        SELECT r.*, u.username as employee_name
        FROM jsh_reimbursement r
        LEFT JOIN jsh_user u ON r.employee_id = u.id
        WHERE r.current_approver = #{currentApprover} 
        AND r.status IN ('SUBMITTED', 'APPROVING')
        AND r.tenant_id = #{tenantId} 
        AND r.delete_flag = '0'
        ORDER BY r.create_time ASC
    </select>

    <!-- 统计报销金额 -->
    <select id="selectReimbursementSummary" resultType="map">
        SELECT 
            DATE_FORMAT(r.apply_date, '%Y-%m') as month,
            SUM(r.total_amount) as total_amount,
            COUNT(*) as count,
            SUM(CASE WHEN r.status = 'APPROVED' THEN r.total_amount ELSE 0 END) as approved_amount,
            SUM(CASE WHEN r.status = 'PAID' THEN r.total_amount ELSE 0 END) as paid_amount
        FROM jsh_reimbursement r
        WHERE r.tenant_id = #{tenantId} AND r.delete_flag = '0'
        <if test="employeeId != null">
            AND r.employee_id = #{employeeId}
        </if>
        <if test="month != null and month != ''">
            AND DATE_FORMAT(r.apply_date, '%Y-%m') = #{month}
        </if>
        GROUP BY DATE_FORMAT(r.apply_date, '%Y-%m')
        ORDER BY month DESC
    </select>

</mapper>
