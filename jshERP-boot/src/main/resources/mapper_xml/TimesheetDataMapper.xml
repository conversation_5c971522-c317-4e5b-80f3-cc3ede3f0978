<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jsh.erp.datasource.mappers.TimesheetDataMapper">
  
  <resultMap id="BaseResultMap" type="com.jsh.erp.datasource.entities.TimesheetData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="work_date" jdbcType="DATE" property="workDate" />
    <result column="regular_hours" jdbcType="DECIMAL" property="regularHours" />
    <result column="overtime_hours" jdbcType="DECIMAL" property="overtimeHours" />
    <result column="leave_hours" jdbcType="DECIMAL" property="leaveHours" />
    <result column="leave_type" jdbcType="VARCHAR" property="leaveType" />
    <result column="attendance_status" jdbcType="VARCHAR" property="attendanceStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="BIGINT" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="BIGINT" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    id, employee_id, employee_name, work_date, regular_hours, overtime_hours, leave_hours, 
    leave_type, attendance_status, remark, tenant_id, delete_flag, create_time, creator, 
    update_time, updater
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_timesheet_data
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByEmployeeAndPeriod" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from jsh_timesheet_data
    where employee_id = #{employeeId,jdbcType=BIGINT}
      and work_date >= #{startDate,jdbcType=DATE}
      and work_date <= #{endDate,jdbcType=DATE}
      and tenant_id = #{tenantId,jdbcType=BIGINT}
      and delete_flag = '0'
    order by work_date
  </select>

  <insert id="insertSelective" parameterType="com.jsh.erp.datasource.entities.TimesheetData">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into jsh_timesheet_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="employeeName != null">
        employee_name,
      </if>
      <if test="workDate != null">
        work_date,
      </if>
      <if test="regularHours != null">
        regular_hours,
      </if>
      <if test="overtimeHours != null">
        overtime_hours,
      </if>
      <if test="leaveHours != null">
        leave_hours,
      </if>
      <if test="leaveType != null">
        leave_type,
      </if>
      <if test="attendanceStatus != null">
        attendance_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="employeeId != null">
        #{employeeId,jdbcType=BIGINT},
      </if>
      <if test="employeeName != null">
        #{employeeName,jdbcType=VARCHAR},
      </if>
      <if test="workDate != null">
        #{workDate,jdbcType=DATE},
      </if>
      <if test="regularHours != null">
        #{regularHours,jdbcType=DECIMAL},
      </if>
      <if test="overtimeHours != null">
        #{overtimeHours,jdbcType=DECIMAL},
      </if>
      <if test="leaveHours != null">
        #{leaveHours,jdbcType=DECIMAL},
      </if>
      <if test="leaveType != null">
        #{leaveType,jdbcType=VARCHAR},
      </if>
      <if test="attendanceStatus != null">
        #{attendanceStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

</mapper>
