package com.jsh.erp.service;

import com.jsh.erp.datasource.entities.*;
import com.jsh.erp.datasource.mappers.*;
import com.jsh.erp.utils.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 薪酬管理核心服务
 */
@Service
public class PayrollService {

    @Resource
    private PayrollElementMapper payrollElementMapper;
    
    @Resource
    private EmployeeSalaryStructureMapper employeeSalaryStructureMapper;
    
    @Resource
    private TimesheetDataMapper timesheetDataMapper;
    
    @Resource
    private PayslipMapper payslipMapper;
    
    @Resource
    private PayslipDetailsMapper payslipDetailsMapper;

    /**
     * 生成工资单 - 核心方法
     * 
     * @param employeeId 员工ID
     * @param payPeriod 薪酬期间 (YYYY-MM)
     * @param tenantId 租户ID
     * @param creator 创建人
     * @return 工资单ID
     */
    @Transactional
    public Long generatePayslip(Long employeeId, String payPeriod, Long tenantId, Long creator) {
        try {
            // 1. 检查是否已存在工资单
            Payslip existingPayslip = payslipMapper.selectByEmployeeAndPeriod(employeeId, payPeriod, tenantId);
            if (existingPayslip != null) {
                throw new RuntimeException("该员工在此期间的工资单已存在");
            }

            // 2. 获取员工薪资结构
            List<EmployeeSalaryStructure> salaryStructure = employeeSalaryStructureMapper.selectByEmployeeId(employeeId, tenantId);
            if (salaryStructure == null || salaryStructure.isEmpty()) {
                throw new RuntimeException("员工薪资结构未配置");
            }

            // 3. 获取考勤数据
            Date[] periodDates = getPeriodDates(payPeriod);
            List<TimesheetData> timesheetList = timesheetDataMapper.selectByEmployeeAndPeriod(
                employeeId, periodDates[0], periodDates[1], tenantId);

            // 4. 计算工作天数和加班小时
            BigDecimal workDays = calculateWorkDays(timesheetList);
            BigDecimal overtimeHours = calculateOvertimeHours(timesheetList);

            // 5. 创建工资单主记录
            Payslip payslip = createPayslipHeader(employeeId, payPeriod, periodDates, workDays, overtimeHours, tenantId, creator);
            payslipMapper.insertSelective(payslip);

            // 6. 计算各薪酬项目并创建明细
            BigDecimal grossSalary = BigDecimal.ZERO;
            BigDecimal totalDeductions = BigDecimal.ZERO;

            for (EmployeeSalaryStructure structure : salaryStructure) {
                PayrollElement element = payrollElementMapper.selectByPrimaryKey(structure.getElementId());
                if (element == null || !"0".equals(element.getStatus())) {
                    continue;
                }

                // 计算薪酬项目金额
                BigDecimal calculatedAmount = calculateElementAmount(element, structure, workDays, overtimeHours);
                
                // 创建工资单明细
                PayslipDetails detail = createPayslipDetail(payslip.getId(), element, structure, calculatedAmount, tenantId, creator);
                payslipDetailsMapper.insertSelective(detail);

                // 累计收入和扣除
                if ("INCOME".equals(element.getElementType())) {
                    grossSalary = grossSalary.add(calculatedAmount);
                } else if ("DEDUCTION".equals(element.getElementType())) {
                    totalDeductions = totalDeductions.add(calculatedAmount);
                }
            }

            // 7. 计算个人所得税（简化版）
            BigDecimal incomeTax = calculateIncomeTax(grossSalary);
            totalDeductions = totalDeductions.add(incomeTax);

            // 8. 计算实发工资
            BigDecimal netSalary = grossSalary.subtract(totalDeductions);

            // 9. 更新工资单汇总信息
            payslip.setGrossSalary(grossSalary);
            payslip.setTotalDeductions(totalDeductions);
            payslip.setIncomeTax(incomeTax);
            payslip.setNetSalary(netSalary);
            payslip.setGeneratedDate(new Date());
            payslipMapper.updateByPrimaryKeySelective(payslip);

            return payslip.getId();

        } catch (Exception e) {
            throw new RuntimeException("生成工资单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算薪酬项目金额
     */
    private BigDecimal calculateElementAmount(PayrollElement element, EmployeeSalaryStructure structure, 
                                            BigDecimal workDays, BigDecimal overtimeHours) {
        BigDecimal amount = BigDecimal.ZERO;
        String calculationRule = element.getCalculationRule();
        BigDecimal amountOrRate = structure.getAmountOrRate();

        switch (calculationRule) {
            case "FIXED":
                // 固定金额
                amount = amountOrRate;
                break;
            case "FORMULA":
                // 公式计算 - 根据项目类型进行不同计算
                amount = calculateFormulaAmount(element, amountOrRate, workDays);
                break;
            case "HOURLY":
                // 按小时计算
                amount = amountOrRate.multiply(overtimeHours);
                break;
            default:
                amount = BigDecimal.ZERO;
        }

        return amount.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 公式计算金额
     */
    private BigDecimal calculateFormulaAmount(PayrollElement element, BigDecimal rate, BigDecimal workDays) {
        String elementCode = element.getElementCode();
        
        // 根据不同的薪酬项目进行计算
        switch (elementCode) {
            case "DAILY_WAGE":
                // 日薪 = 日薪 × 工作天数
                return rate.multiply(workDays);
            case "SALES_COMMISSION_RATE":
                // 销售提成 = 销售额 × 提成比例 (这里用模拟数据)
                return BigDecimal.valueOf(10000).multiply(rate); // 模拟销售额10000元
            case "COFFEE_COMMISSION_RATE":
                // 咖啡店提成 = 咖啡店销售额 × 提成比例
                return BigDecimal.valueOf(5000).multiply(rate); // 模拟咖啡店销售额5000元
            case "CLOISONNE_PRODUCTION_FEE":
                // 掐丝点蓝制作费 = 作品数量 × 单价
                return rate.multiply(BigDecimal.valueOf(3)); // 模拟制作3件作品
            case "ACCESSORY_PRODUCTION_FEE":
                // 配饰制作费 = 作品数量 × 单价
                return rate.multiply(BigDecimal.valueOf(5)); // 模拟制作5件配饰
            case "INSTRUCTOR_FEE_INTERNAL":
                // 在馆讲师费 = 课时 × 课时费
                return rate.multiply(BigDecimal.valueOf(8)); // 模拟8课时
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 计算个人所得税（简化版）
     */
    private BigDecimal calculateIncomeTax(BigDecimal grossSalary) {
        // 简化的个税计算：5000元起征点，超出部分按10%计算
        BigDecimal taxThreshold = BigDecimal.valueOf(5000);
        if (grossSalary.compareTo(taxThreshold) <= 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal taxableAmount = grossSalary.subtract(taxThreshold);
        return taxableAmount.multiply(BigDecimal.valueOf(0.1)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算工作天数
     */
    private BigDecimal calculateWorkDays(List<TimesheetData> timesheetList) {
        if (timesheetList == null || timesheetList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        long presentDays = timesheetList.stream()
            .filter(t -> "PRESENT".equals(t.getAttendanceStatus()))
            .count();
        
        return BigDecimal.valueOf(presentDays);
    }

    /**
     * 计算加班小时
     */
    private BigDecimal calculateOvertimeHours(List<TimesheetData> timesheetList) {
        if (timesheetList == null || timesheetList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return timesheetList.stream()
            .map(TimesheetData::getOvertimeHours)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取期间日期
     */
    private Date[] getPeriodDates(String payPeriod) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Date periodDate = sdf.parse(payPeriod);
            
            Calendar cal = Calendar.getInstance();
            cal.setTime(periodDate);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date startDate = cal.getTime();
            
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date endDate = cal.getTime();
            
            return new Date[]{startDate, endDate};
        } catch (Exception e) {
            throw new RuntimeException("日期解析失败: " + payPeriod, e);
        }
    }

    /**
     * 创建工资单主记录
     */
    private Payslip createPayslipHeader(Long employeeId, String payPeriod, Date[] periodDates, 
                                       BigDecimal workDays, BigDecimal overtimeHours, Long tenantId, Long creator) {
        Payslip payslip = new Payslip();
        payslip.setPayslipNumber(generatePayslipNumber(employeeId, payPeriod));
        payslip.setEmployeeId(employeeId);
        payslip.setEmployeeName(getEmployeeName(employeeId));
        payslip.setPayPeriod(payPeriod);
        payslip.setPayPeriodStart(periodDates[0]);
        payslip.setPayPeriodEnd(periodDates[1]);
        payslip.setWorkDays(workDays);
        payslip.setOvertimeHours(overtimeHours);
        payslip.setStatus("0"); // 草稿状态
        payslip.setTenantId(tenantId);
        payslip.setDeleteFlag("0");
        payslip.setCreator(creator);
        payslip.setCreateTime(new Date());
        
        return payslip;
    }

    /**
     * 创建工资单明细
     */
    private PayslipDetails createPayslipDetail(Long payslipId, PayrollElement element, 
                                             EmployeeSalaryStructure structure, BigDecimal calculatedAmount, 
                                             Long tenantId, Long creator) {
        PayslipDetails detail = new PayslipDetails();
        detail.setPayslipId(payslipId);
        detail.setElementId(element.getId());
        detail.setElementCode(element.getElementCode());
        detail.setElementName(element.getElementName());
        detail.setElementType(element.getElementType());
        detail.setCalculationRate(structure.getAmountOrRate());
        detail.setCalculatedAmount(calculatedAmount);
        detail.setCalculationFormula(element.getFormulaExpression());
        detail.setTenantId(tenantId);
        detail.setDeleteFlag("0");
        detail.setCreator(creator);
        detail.setCreateTime(new Date());
        
        return detail;
    }

    /**
     * 生成工资单号
     */
    private String generatePayslipNumber(Long employeeId, String payPeriod) {
        return "PAY" + payPeriod.replace("-", "") + String.format("%04d", employeeId);
    }

    /**
     * 获取员工姓名
     */
    private String getEmployeeName(Long employeeId) {
        // 这里应该调用用户服务获取员工姓名，暂时返回默认值
        return "员工" + employeeId;
    }
}
