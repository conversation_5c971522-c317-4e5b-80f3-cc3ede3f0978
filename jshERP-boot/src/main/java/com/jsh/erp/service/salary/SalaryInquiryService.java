package com.jsh.erp.service.salary;

import com.jsh.erp.datasource.entities.User;
import com.jsh.erp.datasource.mappers.SalaryCalculationMapperEx;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.service.UserService;
import com.jsh.erp.utils.StringUtil;
import com.jsh.erp.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 薪酬查询服务类
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class SalaryInquiryService {

    private Logger logger = LoggerFactory.getLogger(SalaryInquiryService.class);

    @Resource
    private SalaryCalculationMapperEx salaryCalculationMapperEx;

    @Resource
    private UserService userService;

    /**
     * 获取个人薪酬记录
     */
    public List<Map<String, Object>> getPersonalSalaryRecords(String calculationMonth, String year, 
                                                             HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            
            result = salaryCalculationMapperEx.selectPersonalSalaryRecords(currentUser.getId(), 
                                                                         calculationMonth, year, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取个人薪酬详情
     */
    public Map<String, Object> getPersonalSalaryDetail(Long calculationId, HttpServletRequest request) throws Exception {
        Map<String, Object> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            
            result = salaryCalculationMapperEx.selectPersonalSalaryDetail(calculationId, 
                                                                        currentUser.getId(), tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取薪酬统计信息
     */
    public Map<String, Object> getSalaryStatistics(String year, String department, String statisticsType,
                                                   HttpServletRequest request) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            if ("MONTHLY".equals(statisticsType)) {
                // 月度统计
                List<Map<String, Object>> monthlyData = salaryCalculationMapperEx.selectMonthlyStatistics(year, department, tenantId);
                result.put("monthlyData", monthlyData);
            } else if ("YEARLY".equals(statisticsType)) {
                // 年度统计
                Map<String, Object> yearlyData = salaryCalculationMapperEx.selectYearlyStatistics(year, department, tenantId);
                result.put("yearlyData", yearlyData);
            } else if ("DEPARTMENT".equals(statisticsType)) {
                // 部门统计
                List<Map<String, Object>> departmentData = salaryCalculationMapperEx.selectDepartmentStatistics(year, tenantId);
                result.put("departmentData", departmentData);
            }
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取薪酬趋势分析
     */
    public List<Map<String, Object>> getSalaryTrend(String startMonth, String endMonth, String employeeId,
                                                   HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            Long empId = null;
            if (StringUtil.isNotEmpty(employeeId)) {
                empId = Long.parseLong(employeeId);
            } else {
                // 如果没有指定员工ID，则查询当前用户
                User currentUser = userService.getCurrentUser();
                empId = currentUser.getId();
            }
            
            result = salaryCalculationMapperEx.selectSalaryTrend(empId, startMonth, endMonth, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取部门薪酬对比
     */
    public List<Map<String, Object>> getDepartmentComparison(String calculationMonth, 
                                                            HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = salaryCalculationMapperEx.selectDepartmentComparison(calculationMonth, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取薪酬构成分析
     */
    public Map<String, Object> getSalaryComposition(Long calculationId, HttpServletRequest request) throws Exception {
        Map<String, Object> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = salaryCalculationMapperEx.selectSalaryComposition(calculationId, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 导出个人薪酬记录
     */
    public String exportPersonalSalary(String startMonth, String endMonth, String format,
                                      HttpServletRequest request) throws Exception {
        String fileUrl = "";
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            
            // 获取薪酬记录数据
            List<Map<String, Object>> salaryData = salaryCalculationMapperEx.selectPersonalSalaryForExport(
                currentUser.getId(), startMonth, endMonth, tenantId);
            
            // 生成文件
            if ("PDF".equals(format)) {
                fileUrl = generatePdfReport(salaryData, currentUser.getUsername(), startMonth, endMonth);
            } else if ("EXCEL".equals(format)) {
                fileUrl = generateExcelReport(salaryData, currentUser.getUsername(), startMonth, endMonth);
            }
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return fileUrl;
    }

    /**
     * 获取薪酬排行榜
     */
    public List<Map<String, Object>> getSalaryRanking(String calculationMonth, String department, String rankingType,
                                                     HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = salaryCalculationMapperEx.selectSalaryRanking(calculationMonth, department, rankingType, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取薪酬汇总报表
     */
    public List<Map<String, Object>> getSalarySummary(String calculationMonth, String groupBy,
                                                     HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = salaryCalculationMapperEx.selectSalarySummary(calculationMonth, groupBy, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 生成PDF报表
     */
    private String generatePdfReport(List<Map<String, Object>> data, String employeeName, 
                                   String startMonth, String endMonth) {
        // TODO: 实现PDF生成逻辑
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        return "/files/salary/pdf/" + employeeName + "_" + startMonth + "_" + endMonth + "_" + timestamp + ".pdf";
    }

    /**
     * 生成Excel报表
     */
    private String generateExcelReport(List<Map<String, Object>> data, String employeeName,
                                     String startMonth, String endMonth) {
        // TODO: 实现Excel生成逻辑
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        return "/files/salary/excel/" + employeeName + "_" + startMonth + "_" + endMonth + "_" + timestamp + ".xlsx";
    }
}
