package com.jsh.erp.service.salary;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.ReimbursementItem;
import com.jsh.erp.datasource.mappers.ReimbursementItemMapper;
import com.jsh.erp.exception.JshException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报销明细服务类
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class ReimbursementItemService extends ServiceImpl<ReimbursementItemMapper, ReimbursementItem> {

    private Logger logger = LoggerFactory.getLogger(ReimbursementItemService.class);

    @Resource
    private ReimbursementItemMapper reimbursementItemMapper;

    /**
     * 根据报销申请ID查询明细列表
     */
    public List<ReimbursementItem> getByReimbursementId(Long reimbursementId) throws Exception {
        List<ReimbursementItem> result = null;
        try {
            QueryWrapper<ReimbursementItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("reimbursement_id", reimbursementId);
            queryWrapper.eq("delete_flag", BusinessConstants.DELETE_FLAG_EXISTS);
            queryWrapper.orderByAsc("create_time");
            
            result = reimbursementItemMapper.selectList(queryWrapper);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 根据报销申请ID删除明细（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByReimbursementId(Long reimbursementId) throws Exception {
        int result = 0;
        try {
            QueryWrapper<ReimbursementItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("reimbursement_id", reimbursementId);
            queryWrapper.eq("delete_flag", BusinessConstants.DELETE_FLAG_EXISTS);
            
            List<ReimbursementItem> items = reimbursementItemMapper.selectList(queryWrapper);
            for (ReimbursementItem item : items) {
                item.setDeleteFlag(BusinessConstants.DELETE_FLAG_DELETED);
                result += reimbursementItemMapper.updateById(item);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 批量保存报销明细
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<ReimbursementItem> items) throws Exception {
        int result = 0;
        try {
            for (ReimbursementItem item : items) {
                item.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
                result += reimbursementItemMapper.insert(item);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }
}
