package com.jsh.erp.service.salary;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.Reimbursement;
import com.jsh.erp.datasource.entities.ReimbursementItem;
import com.jsh.erp.datasource.entities.User;
import com.jsh.erp.datasource.mappers.ReimbursementMapper;
import com.jsh.erp.datasource.mappers.ReimbursementMapperEx;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.service.LogService;
import com.jsh.erp.service.UserService;
import com.jsh.erp.utils.StringUtil;
import com.jsh.erp.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 报销管理服务类
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class ReimbursementService extends ServiceImpl<ReimbursementMapper, Reimbursement> {

    private Logger logger = LoggerFactory.getLogger(ReimbursementService.class);

    @Resource
    private ReimbursementMapper reimbursementMapper;

    @Resource
    private ReimbursementMapperEx reimbursementMapperEx;

    @Resource
    private ReimbursementItemService reimbursementItemService;

    @Resource
    private UserService userService;

    @Resource
    private LogService logService;

    /**
     * 根据条件查询报销申请列表
     */
    public List<Reimbursement> select(String employeeName, String department, 
                                     String reimbursementType, String status,
                                     String applyDateStart, String applyDateEnd,
                                     HttpServletRequest request) throws Exception {
        List<Reimbursement> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = reimbursementMapperEx.selectByCondition(employeeName, department, 
                                                           reimbursementType, status,
                                                           applyDateStart, applyDateEnd, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取报销申请详情（包含明细）
     */
    public Reimbursement getReimbursementWithItems(Long id, HttpServletRequest request) throws Exception {
        Reimbursement result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            result = reimbursementMapperEx.selectWithItems(id, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 新增报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertReimbursement(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            // 设置租户ID和创建人信息
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            
            Reimbursement reimbursement = JSONObject.parseObject(obj.toJSONString(), Reimbursement.class);
            
            // 生成报销单号
            String reimbursementNumber = generateReimbursementNumber();
            reimbursement.setReimbursementNumber(reimbursementNumber);
            reimbursement.setTenantId(tenantId);
            reimbursement.setCreateUser(currentUser.getLoginName());
            reimbursement.setCreateTime(new Date());
            reimbursement.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
            reimbursement.setStatus("DRAFT");
            reimbursement.setApproveLevel(0);
            
            // 设置申请人信息
            reimbursement.setEmployeeId(currentUser.getId());
            reimbursement.setEmployeeName(currentUser.getUsername());
            
            result = reimbursementMapper.insert(reimbursement);
            
            // 处理报销明细
            JSONArray itemsArray = obj.getJSONArray("items");
            if (itemsArray != null && itemsArray.size() > 0) {
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (int i = 0; i < itemsArray.size(); i++) {
                    JSONObject itemObj = itemsArray.getJSONObject(i);
                    ReimbursementItem item = JSONObject.parseObject(itemObj.toJSONString(), ReimbursementItem.class);
                    item.setReimbursementId(reimbursement.getId());
                    item.setTenantId(tenantId);
                    item.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
                    
                    reimbursementItemService.save(item);
                    totalAmount = totalAmount.add(item.getAmount());
                }
                
                // 更新总金额
                reimbursement.setTotalAmount(totalAmount);
                reimbursementMapper.updateById(reimbursement);
            }
            
            // 记录操作日志
            logService.insertLog("报销管理", 
                               BusinessConstants.LOG_OPERATION_TYPE_ADD,
                               request);
                               
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 更新报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateReimbursement(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            User currentUser = userService.getCurrentUser();
            Reimbursement reimbursement = JSONObject.parseObject(obj.toJSONString(), Reimbursement.class);
            
            reimbursement.setUpdateUser(currentUser.getLoginName());
            reimbursement.setUpdateTime(new Date());
            
            result = reimbursementMapper.updateById(reimbursement);
            
            // 处理报销明细
            JSONArray itemsArray = obj.getJSONArray("items");
            if (itemsArray != null) {
                // 先删除原有明细
                reimbursementItemService.deleteByReimbursementId(reimbursement.getId());
                
                // 重新添加明细
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (int i = 0; i < itemsArray.size(); i++) {
                    JSONObject itemObj = itemsArray.getJSONObject(i);
                    ReimbursementItem item = JSONObject.parseObject(itemObj.toJSONString(), ReimbursementItem.class);
                    item.setReimbursementId(reimbursement.getId());
                    item.setTenantId(reimbursement.getTenantId());
                    item.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
                    
                    reimbursementItemService.save(item);
                    totalAmount = totalAmount.add(item.getAmount());
                }
                
                // 更新总金额
                reimbursement.setTotalAmount(totalAmount);
                reimbursementMapper.updateById(reimbursement);
            }
            
            // 记录操作日志
            logService.insertLog("报销管理", 
                               BusinessConstants.LOG_OPERATION_TYPE_EDIT,
                               request);
                               
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 删除报销申请（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteReimbursement(Long id, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Reimbursement reimbursement = reimbursementMapper.selectById(id);
            if (reimbursement != null) {
                reimbursement.setDeleteFlag(BusinessConstants.DELETE_FLAG_DELETED);
                reimbursement.setUpdateTime(new Date());
                
                User currentUser = userService.getCurrentUser();
                reimbursement.setUpdateUser(currentUser.getLoginName());
                
                result = reimbursementMapper.updateById(reimbursement);
                
                // 同时删除明细
                reimbursementItemService.deleteByReimbursementId(id);
                
                // 记录操作日志
                logService.insertLog("报销管理", 
                                   BusinessConstants.LOG_OPERATION_TYPE_DELETE,
                                   request);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 批量删除报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteReimbursement(String ids, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            List<Long> idList = StringUtil.strToLongList(ids);
            for (Long id : idList) {
                result += deleteReimbursement(id, request);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 提交报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int submitReimbursement(Long id, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Reimbursement reimbursement = reimbursementMapper.selectById(id);
            if (reimbursement != null && "DRAFT".equals(reimbursement.getStatus())) {
                reimbursement.setStatus("SUBMITTED");
                reimbursement.setApproveLevel(1);
                reimbursement.setUpdateTime(new Date());
                
                // TODO: 根据审批流程配置设置当前审批人
                
                result = reimbursementMapper.updateById(reimbursement);
                
                // 记录操作日志
                logService.insertLog("报销管理", "提交申请", request);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 审批报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int approveReimbursement(Long id, String action, String remark, 
                                   HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Reimbursement reimbursement = reimbursementMapper.selectById(id);
            if (reimbursement != null) {
                if ("APPROVE".equals(action)) {
                    // TODO: 根据审批流程判断是否还有下一级审批
                    reimbursement.setStatus("APPROVED");
                } else if ("REJECT".equals(action)) {
                    reimbursement.setStatus("REJECTED");
                }
                
                reimbursement.setUpdateTime(new Date());
                result = reimbursementMapper.updateById(reimbursement);
                
                // TODO: 记录审批记录
                
                // 记录操作日志
                logService.insertLog("报销管理", "审批操作", request);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 支付报销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public int payReimbursement(Long id, String paymentMethod, String paymentAccount,
                               HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Reimbursement reimbursement = reimbursementMapper.selectById(id);
            if (reimbursement != null && "APPROVED".equals(reimbursement.getStatus())) {
                reimbursement.setStatus("PAID");
                reimbursement.setPaymentMethod(paymentMethod);
                reimbursement.setPaymentAccount(paymentAccount);
                reimbursement.setPaymentDate(new Date());
                
                User currentUser = userService.getCurrentUser();
                reimbursement.setPaymentUser(currentUser.getLoginName());
                reimbursement.setUpdateTime(new Date());
                
                result = reimbursementMapper.updateById(reimbursement);
                
                // 记录操作日志
                logService.insertLog("报销管理", "支付操作", request);
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 获取我的报销申请列表
     */
    public List<Reimbursement> selectMyReimbursements(String status, String applyDateStart, 
                                                     String applyDateEnd, HttpServletRequest request) throws Exception {
        List<Reimbursement> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            result = reimbursementMapperEx.selectMyReimbursements(currentUser.getId(), status, 
                                                                applyDateStart, applyDateEnd, tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取待我审批的报销申请列表
     */
    public List<Reimbursement> selectPendingApprovals(HttpServletRequest request) throws Exception {
        List<Reimbursement> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            result = reimbursementMapperEx.selectPendingApprovals(currentUser.getLoginName(), tenantId);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 生成报销单号
     */
    private String generateReimbursementNumber() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = "BX" + dateStr;
        
        // TODO: 实现序号生成逻辑，确保唯一性
        long sequence = System.currentTimeMillis() % 10000;
        return prefix + String.format("%04d", sequence);
    }
}
