package com.jsh.erp.service.salary.strategy.impl;

import com.jsh.erp.datasource.entities.SalaryItem;
import com.jsh.erp.service.salary.dto.EmployeeData;
import com.jsh.erp.service.salary.dto.SalaryCalculationResult;
import com.jsh.erp.service.salary.strategy.SalaryCalculationStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 生产提成计算策略
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Component
public class ProductionCommissionCalculationStrategy implements SalaryCalculationStrategy {

    private Logger logger = LoggerFactory.getLogger(ProductionCommissionCalculationStrategy.class);

    @Override
    public String getSupportedItemCode() {
        return "PRODUCTION_COMMISSION";
    }

    @Override
    public String getSupportedItemType() {
        return "COMMISSION";
    }

    @Override
    public String getStrategyName() {
        return "生产提成";
    }

    @Override
    public String getStrategyDescription() {
        return "根据生产产值和提成比例计算生产提成";
    }

    @Override
    public SalaryCalculationResult calculate(EmployeeData employeeData, SalaryItem salaryItem, 
                                           String calculationMonth, Long tenantId) throws Exception {
        try {
            // 验证计算条件
            if (!validateConditions(employeeData, salaryItem)) {
                return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                    "计算条件不满足：缺少生产数据或提成比例");
            }

            // 获取生产数据
            Map<String, Object> productionData = employeeData.getProductionData();
            BigDecimal cloisonneProduction = (BigDecimal) productionData.getOrDefault("cloisonneProduction", BigDecimal.ZERO);
            BigDecimal accessoryProduction = (BigDecimal) productionData.getOrDefault("accessoryProduction", BigDecimal.ZERO);
            
            // 计算总生产产值
            BigDecimal totalProductionAmount = cloisonneProduction.add(accessoryProduction);

            // 获取提成比例
            BigDecimal commissionRate = getCommissionRate(salaryItem);
            
            // 计算提成金额
            BigDecimal calculatedAmount = totalProductionAmount.multiply(commissionRate)
                .setScale(2, RoundingMode.HALF_UP);
            
            // 创建计算结果
            SalaryCalculationResult result = SalaryCalculationResult.success(
                getSupportedItemCode(), getStrategyName(), calculatedAmount);
            
            result.setItemType("COMMISSION");
            result.setBaseAmount(totalProductionAmount);
            result.setRate(commissionRate);
            result.setCalculationAmount(calculatedAmount);
            result.setFinalAmount(calculatedAmount);
            result.setCalculationFormula(getCalculationFormula(salaryItem));
            result.setRemark(String.format("生产产值%s元 × 提成比例%s%% (景泰蓝%s元 + 配饰%s元)", 
                totalProductionAmount, commissionRate.multiply(BigDecimal.valueOf(100)),
                cloisonneProduction, accessoryProduction));
            
            // 设置计算详情
            Map<String, Object> details = new HashMap<>();
            details.put("cloisonneProduction", cloisonneProduction);
            details.put("accessoryProduction", accessoryProduction);
            details.put("totalProductionAmount", totalProductionAmount);
            details.put("commissionRate", commissionRate);
            details.put("calculationMonth", calculationMonth);
            result.setDetails(details);
            
            logger.debug("员工{}生产提成计算完成：{}元", employeeData.getEmployeeId(), calculatedAmount);
            
            return result;
            
        } catch (Exception e) {
            logger.error("员工{}生产提成计算失败", employeeData.getEmployeeId(), e);
            return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                "计算过程发生异常：" + e.getMessage());
        }
    }

    @Override
    public boolean validateConditions(EmployeeData employeeData, SalaryItem salaryItem) {
        // 检查生产数据
        Map<String, Object> productionData = employeeData.getProductionData();
        if (productionData == null) {
            logger.warn("员工{}生产数据缺失", employeeData.getEmployeeId());
            return false;
        }
        
        // 检查提成比例
        BigDecimal commissionRate = getCommissionRate(salaryItem);
        if (commissionRate == null || commissionRate.compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("员工{}生产提成比例未设置", employeeData.getEmployeeId());
            return false;
        }
        
        return true;
    }

    @Override
    public String getCalculationFormula(SalaryItem salaryItem) {
        BigDecimal rate = getCommissionRate(salaryItem);
        return String.format("生产提成 = (景泰蓝生产产值 + 配饰生产产值) × %s%%", 
            rate != null ? rate.multiply(BigDecimal.valueOf(100)) : "提成比例");
    }

    /**
     * 获取提成比例
     */
    private BigDecimal getCommissionRate(SalaryItem salaryItem) {
        // 优先从薪酬项目配置获取
        if (salaryItem != null && salaryItem.getDefaultValue() != null) {
            return salaryItem.getDefaultValue();
        }
        
        // 默认提成比例3%
        return BigDecimal.valueOf(0.03);
    }
}
