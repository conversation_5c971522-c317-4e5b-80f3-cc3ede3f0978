package com.jsh.erp.service.salary;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.ReimbursementType;
import com.jsh.erp.datasource.entities.SalaryProfile;
import com.jsh.erp.datasource.entities.User;
import com.jsh.erp.datasource.mappers.ReimbursementTypeMapper;
import com.jsh.erp.datasource.mappers.SalaryItemTemplateMapper;
import com.jsh.erp.datasource.mappers.SalaryProfileMapper;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.service.LogService;
import com.jsh.erp.service.UserService;
import com.jsh.erp.utils.StringUtil;
import com.jsh.erp.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 薪酬配置管理服务类
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class SalaryConfigService {

    private Logger logger = LoggerFactory.getLogger(SalaryConfigService.class);

    @Resource
    private SalaryItemTemplateMapper salaryItemTemplateMapper;

    @Resource
    private ReimbursementTypeMapper reimbursementTypeMapper;

    @Resource
    private SalaryProfileMapper salaryProfileMapper;

    @Resource
    private UserService userService;

    @Resource
    private LogService logService;

    // ========== 薪酬项目模板管理 ==========

    /**
     * 获取薪酬项目模板列表
     */
    public List<Map<String, Object>> getItemTemplateList(String itemType, String status, 
                                                         HttpServletRequest request) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.eq("delete_flag", BusinessConstants.DELETE_FLAG_EXISTS);
            
            if (StringUtil.isNotEmpty(itemType)) {
                queryWrapper.eq("item_type", itemType);
            }
            if (StringUtil.isNotEmpty(status)) {
                queryWrapper.eq("status", status);
            }
            
            queryWrapper.orderByAsc("sort_order");
            
            // TODO: 实现具体的查询逻辑
            // List<SalaryItemTemplate> templates = salaryItemTemplateMapper.selectList(queryWrapper);
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存薪酬项目模板
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveItemTemplate(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            User currentUser = userService.getCurrentUser();
            
            // TODO: 实现保存逻辑
            
            // 记录操作日志
            logService.insertLog("薪酬项目模板管理", BusinessConstants.LOG_OPERATION_TYPE_ADD, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 删除薪酬项目模板
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteItemTemplate(Long id, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            // TODO: 实现删除逻辑
            
            // 记录操作日志
            logService.insertLog("薪酬项目模板管理", BusinessConstants.LOG_OPERATION_TYPE_DELETE, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    // ========== 报销类型管理 ==========

    /**
     * 获取报销类型列表
     */
    public List<ReimbursementType> getReimbursementTypeList(String status, HttpServletRequest request) throws Exception {
        List<ReimbursementType> result = null;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            QueryWrapper<ReimbursementType> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            queryWrapper.eq("delete_flag", BusinessConstants.DELETE_FLAG_EXISTS);
            
            if (StringUtil.isNotEmpty(status)) {
                queryWrapper.eq("status", status);
            }
            
            queryWrapper.orderByAsc("sort_order");
            
            result = reimbursementTypeMapper.selectList(queryWrapper);
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存报销类型
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveReimbursementType(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            ReimbursementType reimbursementType = JSONObject.parseObject(obj.toJSONString(), ReimbursementType.class);
            reimbursementType.setTenantId(tenantId);
            reimbursementType.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
            
            if (reimbursementType.getId() == null) {
                // 新增
                result = reimbursementTypeMapper.insert(reimbursementType);
            } else {
                // 更新
                result = reimbursementTypeMapper.updateById(reimbursementType);
            }
            
            // 记录操作日志
            logService.insertLog("报销类型管理", BusinessConstants.LOG_OPERATION_TYPE_ADD, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    // ========== 计算引擎配置 ==========

    /**
     * 获取计算引擎配置
     */
    public Map<String, Object> getCalculationEngineConfig(HttpServletRequest request) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 从jsh_calculation_engine_config表查询配置
            // 暂时返回默认配置
            result.put("batchSize", 50);
            result.put("timeout", 300);
            result.put("maxConcurrency", 3);
            result.put("retryTimes", 3);
            result.put("salesTable", "jsh_sales_record");
            result.put("trainingTable", "jsh_training_record");
            result.put("productionTable", "jsh_production_record");
            result.put("reimbursementTable", "jsh_reimbursement");
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存计算引擎配置
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveCalculationEngineConfig(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 保存到jsh_calculation_engine_config表
            result = 1; // 临时返回成功
            
            // 记录操作日志
            logService.insertLog("计算引擎配置", BusinessConstants.LOG_OPERATION_TYPE_EDIT, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    // ========== 数据匹配逻辑配置 ==========

    /**
     * 获取数据匹配逻辑配置
     */
    public Map<String, Object> getMatchingLogicConfig(HttpServletRequest request) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 从配置表查询
            // 暂时返回默认配置
            result.put("employeeField", "employee_id");
            result.put("timeField", "record_date");
            result.put("timeFormat", "YYYY-MM");
            result.put("salesStatus", Arrays.asList("CONFIRMED", "PAID"));
            result.put("trainingStatus", Arrays.asList("COMPLETED", "CONFIRMED"));
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存数据匹配逻辑配置
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveMatchingLogicConfig(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 保存配置
            result = 1; // 临时返回成功
            
            // 记录操作日志
            logService.insertLog("数据匹配逻辑配置", BusinessConstants.LOG_OPERATION_TYPE_EDIT, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    // ========== 审批流程配置 ==========

    /**
     * 获取审批流程配置
     */
    public Map<String, Object> getApprovalProcessConfig(String processType, HttpServletRequest request) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 从jsh_approval_process_config表查询
            // 暂时返回默认配置
            if ("REIMBURSEMENT".equals(processType)) {
                result.put("enabled", true);
                result.put("levels", 2);
                result.put("autoApproval", true);
                result.put("amountThreshold", 200);
            } else if ("SALARY".equals(processType)) {
                result.put("enabled", true);
                result.put("mode", "BATCH");
                result.put("autoApprovalThreshold", 3000);
            }
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存审批流程配置
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveApprovalProcessConfig(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 保存到jsh_approval_process_config表
            result = 1; // 临时返回成功
            
            // 记录操作日志
            logService.insertLog("审批流程配置", BusinessConstants.LOG_OPERATION_TYPE_EDIT, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    // ========== 默认设置 ==========

    /**
     * 获取默认设置
     */
    public Map<String, Object> getDefaultSettings(HttpServletRequest request) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 从配置表查询默认设置
            // 暂时返回默认值
            result.put("defaultDailyWage", 200);
            result.put("defaultCoffeeCommission", 0.05);
            result.put("autoApply", true);
            
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 保存默认设置
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveDefaultSettings(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            // TODO: 保存默认设置
            result = 1; // 临时返回成功
            
            // 记录操作日志
            logService.insertLog("默认设置", BusinessConstants.LOG_OPERATION_TYPE_EDIT, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 批量配置员工薪酬
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchConfigSalary(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = Tools.getTenantIdByToken(request.getHeader("X-Access-Token"));
            
            JSONArray employeeIds = obj.getJSONArray("employeeIds");
            JSONObject config = obj.getJSONObject("config");
            String mode = obj.getString("mode"); // REPLACE, MERGE, UPDATE
            
            for (int i = 0; i < employeeIds.size(); i++) {
                Long employeeId = employeeIds.getLong(i);
                
                // 查询现有配置
                QueryWrapper<SalaryProfile> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("employee_id", employeeId);
                queryWrapper.eq("tenant_id", tenantId);
                queryWrapper.eq("delete_flag", BusinessConstants.DELETE_FLAG_EXISTS);
                
                SalaryProfile existingProfile = salaryProfileMapper.selectOne(queryWrapper);
                
                if (existingProfile == null) {
                    // 创建新配置
                    SalaryProfile newProfile = new SalaryProfile();
                    newProfile.setEmployeeId(employeeId);
                    newProfile.setTenantId(tenantId);
                    newProfile.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
                    
                    // 应用配置
                    applyConfigToProfile(newProfile, config);
                    
                    result += salaryProfileMapper.insert(newProfile);
                } else {
                    // 更新现有配置
                    if ("REPLACE".equals(mode)) {
                        // 替换模式：完全替换
                        applyConfigToProfile(existingProfile, config);
                    } else if ("MERGE".equals(mode)) {
                        // 合并模式：只更新非空字段
                        mergeConfigToProfile(existingProfile, config);
                    } else if ("UPDATE".equals(mode)) {
                        // 更新模式：只更新指定字段
                        updateConfigToProfile(existingProfile, config);
                    }
                    
                    result += salaryProfileMapper.updateById(existingProfile);
                }
            }
            
            // 记录操作日志
            logService.insertLog("批量配置薪酬", BusinessConstants.LOG_OPERATION_TYPE_EDIT, request);
            
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 应用配置到薪酬档案
     */
    private void applyConfigToProfile(SalaryProfile profile, JSONObject config) {
        if (config.containsKey("dailyWage")) {
            profile.setDailyWage(config.getBigDecimal("dailyWage"));
            profile.setDailyWageEnabled(true);
        }
        if (config.containsKey("coffeeCommissionRate")) {
            profile.setCoffeeCommissionRate(config.getBigDecimal("coffeeCommissionRate"));
            profile.setCoffeeCommissionEnabled(true);
        }
        // TODO: 添加其他字段的配置
    }

    /**
     * 合并配置到薪酬档案
     */
    private void mergeConfigToProfile(SalaryProfile profile, JSONObject config) {
        // TODO: 实现合并逻辑
    }

    /**
     * 更新配置到薪酬档案
     */
    private void updateConfigToProfile(SalaryProfile profile, JSONObject config) {
        // TODO: 实现更新逻辑
    }
}
