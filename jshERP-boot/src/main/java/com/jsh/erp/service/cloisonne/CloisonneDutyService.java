package com.jsh.erp.service.cloisonne;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.constants.ExceptionConstants;
import com.jsh.erp.datasource.entities.CloisonneDuty;
import com.jsh.erp.datasource.entities.Person;
import com.jsh.erp.datasource.entities.User;
import com.jsh.erp.datasource.mappers.CloisonneDutyMapper;
import com.jsh.erp.exception.BusinessRunTimeException;
import com.jsh.erp.exception.JshException;
import com.jsh.erp.service.log.LogService;
import com.jsh.erp.service.person.PersonService;
import com.jsh.erp.service.user.UserService;
import com.jsh.erp.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 掐丝珐琅馆值班管理服务类
 */
@Service
public class CloisonneDutyService {

    private Logger logger = LoggerFactory.getLogger(CloisonneDutyService.class);

    @Resource
    private CloisonneDutyMapper cloisonneDutyMapper;

    @Resource
    private PersonService personService;

    @Resource
    private UserService userService;

    @Resource
    private LogService logService;

    /**
     * 根据ID查询值班记录
     */
    public CloisonneDuty getCloisonneDuty(Long id) throws Exception {
        CloisonneDuty result = null;
        try {
            result = cloisonneDutyMapper.selectByPrimaryKey(id);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 根据条件查询值班记录列表
     */
    public List<CloisonneDuty> select(String search, Integer currentPage, Integer pageSize) throws Exception {
        List<CloisonneDuty> dataList = new ArrayList<>();
        try {
            Long tenantId = getCurrentTenantId();
            
            // 解析查询条件
            JSONObject searchObj = StringUtil.getInfo(search);
            LocalDate startDate = null;
            LocalDate endDate = null;
            Long employeeId = null;
            String shiftType = null;
            String status = null;
            
            if (searchObj != null) {
                if (searchObj.getString("startDate") != null) {
                    startDate = LocalDate.parse(searchObj.getString("startDate"));
                }
                if (searchObj.getString("endDate") != null) {
                    endDate = LocalDate.parse(searchObj.getString("endDate"));
                }
                if (searchObj.getString("employeeId") != null) {
                    employeeId = searchObj.getLong("employeeId");
                }
                shiftType = searchObj.getString("shiftType");
                status = searchObj.getString("status");
            }
            
            dataList = cloisonneDutyMapper.selectByCondition(tenantId, startDate, endDate, employeeId, shiftType, status);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return dataList;
    }

    /**
     * 根据年月查询值班记录
     */
    public List<CloisonneDuty> selectByYearMonth(Integer year, Integer month) throws Exception {
        List<CloisonneDuty> dataList = new ArrayList<>();
        try {
            Long tenantId = getCurrentTenantId();
            dataList = cloisonneDutyMapper.selectByYearMonth(tenantId, year, month);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return dataList;
    }

    /**
     * 新增值班记录
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int insertCloisonneDuty(JSONObject obj, HttpServletRequest request) throws Exception {
        CloisonneDuty cloisonneDuty = JSONObject.parseObject(obj.toJSONString(), CloisonneDuty.class);
        int result = 0;
        try {
            // 设置租户ID和基础信息
            cloisonneDuty.setTenantId(getCurrentTenantId());
            cloisonneDuty.setCreateTime(new Date());
            cloisonneDuty.setUpdateTime(new Date());
            cloisonneDuty.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
            
            // 设置创建用户
            User userInfo = userService.getCurrentUser();
            if (userInfo != null) {
                cloisonneDuty.setCreateUser(userInfo.getId());
                cloisonneDuty.setUpdateUser(userInfo.getId());
            }
            
            // 获取员工姓名
            if (cloisonneDuty.getEmployeeId() != null) {
                Person person = personService.getPerson(cloisonneDuty.getEmployeeId());
                if (person != null) {
                    cloisonneDuty.setEmployeeName(person.getName());
                }
            }
            
            // 检查值班冲突
            checkDutyConflict(cloisonneDuty);
            
            // 设置默认值
            if (cloisonneDuty.getStatus() == null) {
                cloisonneDuty.setStatus("normal");
            }
            if (cloisonneDuty.getPriority() == null) {
                cloisonneDuty.setPriority("normal");
            }
            
            result = cloisonneDutyMapper.insertSelective(cloisonneDuty);
            logService.insertLog("珐琅馆值班", 
                BusinessConstants.LOG_OPERATION_TYPE_ADD, 
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 更新值班记录
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int updateCloisonneDuty(JSONObject obj, HttpServletRequest request) throws Exception {
        CloisonneDuty cloisonneDuty = JSONObject.parseObject(obj.toJSONString(), CloisonneDuty.class);
        int result = 0;
        try {
            // 设置租户ID和更新信息
            cloisonneDuty.setTenantId(getCurrentTenantId());
            cloisonneDuty.setUpdateTime(new Date());
            
            // 设置更新用户
            User userInfo = userService.getCurrentUser();
            if (userInfo != null) {
                cloisonneDuty.setUpdateUser(userInfo.getId());
            }
            
            // 获取员工姓名
            if (cloisonneDuty.getEmployeeId() != null) {
                Person person = personService.getPerson(cloisonneDuty.getEmployeeId());
                if (person != null) {
                    cloisonneDuty.setEmployeeName(person.getName());
                }
            }
            
            // 检查值班冲突（排除当前记录）
            checkDutyConflictForUpdate(cloisonneDuty);
            
            result = cloisonneDutyMapper.updateByPrimaryKeySelective(cloisonneDuty);
            logService.insertLog("珐琅馆值班", 
                BusinessConstants.LOG_OPERATION_TYPE_EDIT, 
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 删除值班记录
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int deleteCloisonneDuty(Long id, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            result = cloisonneDutyMapper.deleteByPrimaryKey(id);
            logService.insertLog("珐琅馆值班", 
                BusinessConstants.LOG_OPERATION_TYPE_DELETE, 
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 批量删除值班记录
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int batchDeleteCloisonneDuty(String ids, HttpServletRequest request) throws Exception {
        List<Long> idList = StringUtil.strToLongList(ids);
        int result = 0;
        try {
            Long tenantId = getCurrentTenantId();
            result = cloisonneDutyMapper.batchDelete(idList, tenantId);
            logService.insertLog("珐琅馆值班", 
                BusinessConstants.LOG_OPERATION_TYPE_DELETE, 
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 批量新增值班记录
     */
    @Transactional(value = "transactionManager", rollbackFor = Exception.class)
    public int batchInsertCloisonneDuty(JSONObject obj, HttpServletRequest request) throws Exception {
        int result = 0;
        try {
            Long tenantId = getCurrentTenantId();
            User userInfo = userService.getCurrentUser();
            
            // 解析批量数据
            List<Long> employeeIds = (List<Long>) obj.get("employeeIds");
            LocalDate startDate = LocalDate.parse(obj.getString("startDate"));
            LocalDate endDate = LocalDate.parse(obj.getString("endDate"));
            String shiftType = obj.getString("shiftType");
            LocalTime startTime = LocalTime.parse(obj.getString("startTime"));
            LocalTime endTime = LocalTime.parse(obj.getString("endTime"));
            BigDecimal workHours = obj.getBigDecimal("workHours");
            String status = obj.getString("status");
            String notes = obj.getString("notes");
            Boolean skipConflicts = obj.getBoolean("skipConflicts");
            
            List<CloisonneDuty> dutyList = new ArrayList<>();
            
            // 生成批量值班记录
            for (Long employeeId : employeeIds) {
                Person person = personService.getPerson(employeeId);
                String employeeName = person != null ? person.getName() : "";
                
                LocalDate currentDate = startDate;
                while (!currentDate.isAfter(endDate)) {
                    // 检查冲突
                    boolean hasConflict = false;
                    if (!skipConflicts) {
                        int conflictCount = cloisonneDutyMapper.checkDutyConflict(
                            tenantId, employeeId, currentDate, 
                            startTime.toString(), endTime.toString(), null);
                        hasConflict = conflictCount > 0;
                    }
                    
                    if (!hasConflict) {
                        CloisonneDuty duty = new CloisonneDuty();
                        duty.setScheduleDate(currentDate);
                        duty.setEmployeeId(employeeId);
                        duty.setEmployeeName(employeeName);
                        duty.setShiftType(shiftType);
                        duty.setStartTime(startTime);
                        duty.setEndTime(endTime);
                        duty.setWorkHours(workHours);
                        duty.setStatus(status != null ? status : "normal");
                        duty.setPriority("normal");
                        duty.setNotes(notes);
                        duty.setTenantId(tenantId);
                        duty.setCreateTime(new Date());
                        duty.setUpdateTime(new Date());
                        duty.setCreateUser(userInfo != null ? userInfo.getId() : null);
                        duty.setUpdateUser(userInfo != null ? userInfo.getId() : null);
                        duty.setDeleteFlag(BusinessConstants.DELETE_FLAG_EXISTS);
                        
                        dutyList.add(duty);
                    }
                    
                    currentDate = currentDate.plusDays(1);
                }
            }
            
            if (!dutyList.isEmpty()) {
                result = cloisonneDutyMapper.batchInsert(dutyList);
                logService.insertLog("珐琅馆值班", 
                    BusinessConstants.LOG_OPERATION_TYPE_ADD, 
                    ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
            }
        } catch (Exception e) {
            JshException.writeFail(logger, e);
        }
        return result;
    }

    /**
     * 检查值班冲突
     */
    private void checkDutyConflict(CloisonneDuty duty) throws Exception {
        Long tenantId = getCurrentTenantId();
        int conflictCount = cloisonneDutyMapper.checkDutyConflict(
            tenantId, duty.getEmployeeId(), duty.getScheduleDate(),
            duty.getStartTime().toString(), duty.getEndTime().toString(), null);
        
        if (conflictCount > 0) {
            throw new BusinessRunTimeException(ExceptionConstants.CLOISONNE_DUTY_CONFLICT_CODE,
                String.format("员工 %s 在 %s 的时间段已有排班安排", 
                    duty.getEmployeeName(), duty.getScheduleDate()));
        }
    }

    /**
     * 检查值班冲突（更新时）
     */
    private void checkDutyConflictForUpdate(CloisonneDuty duty) throws Exception {
        Long tenantId = getCurrentTenantId();
        int conflictCount = cloisonneDutyMapper.checkDutyConflict(
            tenantId, duty.getEmployeeId(), duty.getScheduleDate(),
            duty.getStartTime().toString(), duty.getEndTime().toString(), duty.getId());
        
        if (conflictCount > 0) {
            throw new BusinessRunTimeException(ExceptionConstants.CLOISONNE_DUTY_CONFLICT_CODE,
                String.format("员工 %s 在 %s 的时间段已有排班安排", 
                    duty.getEmployeeName(), duty.getScheduleDate()));
        }
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() throws Exception {
        User userInfo = userService.getCurrentUser();
        return userInfo != null ? userInfo.getTenantId() : 0L;
    }

    /**
     * 获取员工列表
     */
    public List<Person> getEmployeeList() throws Exception {
        List<Person> result = new ArrayList<>();
        try {
            // 获取所有员工类型的人员
            result = personService.getPersonByType("employee");
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }

    /**
     * 获取值班统计数据
     */
    public Map<String, Object> getStatistics(String startDate, String endDate) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try {
            Long tenantId = getCurrentTenantId();
            LocalDate start = startDate != null ? LocalDate.parse(startDate) : null;
            LocalDate end = endDate != null ? LocalDate.parse(endDate) : null;
            
            List<CloisonneDuty> dataList = cloisonneDutyMapper.getEmployeeStatistics(tenantId, start, end);
            
            // 统计处理
            Map<String, Map<String, Object>> employeeStats = new HashMap<>();
            int totalDays = 0;
            
            for (CloisonneDuty duty : dataList) {
                String employeeName = duty.getEmployeeName();
                if (!employeeStats.containsKey(employeeName)) {
                    Map<String, Object> stats = new HashMap<>();
                    stats.put("name", employeeName);
                    stats.put("days", 0);
                    stats.put("shifts", new HashMap<String, Integer>());
                    employeeStats.put(employeeName, stats);
                }
                
                Map<String, Object> stats = employeeStats.get(employeeName);
                stats.put("days", (Integer) stats.get("days") + 1);
                
                Map<String, Integer> shifts = (Map<String, Integer>) stats.get("shifts");
                String shiftType = duty.getShiftType();
                shifts.put(shiftType, shifts.getOrDefault(shiftType, 0) + 1);
                
                totalDays++;
            }
            
            result.put("employeeStats", new ArrayList<>(employeeStats.values()));
            result.put("totalDays", totalDays);
            result.put("employeeCount", employeeStats.size());
            result.put("averageDays", employeeStats.size() > 0 ? (double) totalDays / employeeStats.size() : 0);
        } catch (Exception e) {
            JshException.readFail(logger, e);
        }
        return result;
    }
}
