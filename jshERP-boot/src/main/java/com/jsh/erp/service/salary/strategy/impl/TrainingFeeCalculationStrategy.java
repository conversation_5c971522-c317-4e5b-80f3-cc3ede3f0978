package com.jsh.erp.service.salary.strategy.impl;

import com.jsh.erp.datasource.entities.SalaryItem;
import com.jsh.erp.service.salary.dto.EmployeeData;
import com.jsh.erp.service.salary.dto.SalaryCalculationResult;
import com.jsh.erp.service.salary.strategy.SalaryCalculationStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 培训费用计算策略
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Component
public class TrainingFeeCalculationStrategy implements SalaryCalculationStrategy {

    private Logger logger = LoggerFactory.getLogger(TrainingFeeCalculationStrategy.class);

    @Override
    public String getSupportedItemCode() {
        return "TRAINING_FEE";
    }

    @Override
    public String getStrategyName() {
        return "培训费用";
    }

    @Override
    public String getStrategyDescription() {
        return "根据培训小时数和小时费率计算培训费用";
    }

    @Override
    public SalaryCalculationResult calculate(EmployeeData employeeData, SalaryItem salaryItem, 
                                           String calculationMonth, Long tenantId) throws Exception {
        try {
            // 验证计算条件
            if (!validateConditions(employeeData, salaryItem)) {
                return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                    "计算条件不满足：缺少培训数据或费率设置");
            }

            // 获取培训数据
            Map<String, Object> trainingData = employeeData.getTrainingData();
            Integer instructorHours = (Integer) trainingData.getOrDefault("instructorHours", 0);
            Integer assistantHours = (Integer) trainingData.getOrDefault("assistantHours", 0);
            
            // 获取费率配置
            BigDecimal instructorRate = getInstructorRate(salaryItem);
            BigDecimal assistantRate = getAssistantRate(salaryItem);
            
            // 计算培训费用
            BigDecimal instructorFee = instructorRate.multiply(BigDecimal.valueOf(instructorHours));
            BigDecimal assistantFee = assistantRate.multiply(BigDecimal.valueOf(assistantHours));
            BigDecimal totalFee = instructorFee.add(assistantFee).setScale(2, RoundingMode.HALF_UP);
            
            // 创建计算结果
            SalaryCalculationResult result = SalaryCalculationResult.success(
                getSupportedItemCode(), getStrategyName(), totalFee);
            
            result.setItemType("COMMISSION");
            result.setBaseAmount(BigDecimal.valueOf(instructorHours + assistantHours));
            result.setCalculatedAmount(totalFee);
            result.setFinalAmount(totalFee);
            result.setCalculationFormula(getCalculationFormula(salaryItem));
            result.setRemark(String.format("讲师%d小时×%s元 + 助教%d小时×%s元", 
                instructorHours, instructorRate, assistantHours, assistantRate));
            
            // 设置计算详情
            Map<String, Object> details = new HashMap<>();
            details.put("instructorHours", instructorHours);
            details.put("assistantHours", assistantHours);
            details.put("instructorRate", instructorRate);
            details.put("assistantRate", assistantRate);
            details.put("instructorFee", instructorFee);
            details.put("assistantFee", assistantFee);
            details.put("totalHours", instructorHours + assistantHours);
            details.put("calculationMonth", calculationMonth);
            result.setDetails(details);
            
            logger.debug("员工{}培训费用计算完成：{}元", employeeData.getEmployeeId(), totalFee);
            
            return result;
            
        } catch (Exception e) {
            logger.error("员工{}培训费用计算失败", employeeData.getEmployeeId(), e);
            return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                "计算过程发生异常：" + e.getMessage());
        }
    }

    @Override
    public boolean validateConditions(EmployeeData employeeData, SalaryItem salaryItem) {
        // 检查培训数据
        Map<String, Object> trainingData = employeeData.getTrainingData();
        if (trainingData == null) {
            logger.warn("员工{}培训数据缺失", employeeData.getEmployeeId());
            return false;
        }
        
        // 检查费率设置
        BigDecimal instructorRate = getInstructorRate(salaryItem);
        BigDecimal assistantRate = getAssistantRate(salaryItem);
        
        if ((instructorRate == null || instructorRate.compareTo(BigDecimal.ZERO) <= 0) &&
            (assistantRate == null || assistantRate.compareTo(BigDecimal.ZERO) <= 0)) {
            logger.warn("员工{}培训费率未设置", employeeData.getEmployeeId());
            return false;
        }
        
        return true;
    }

    @Override
    public String getCalculationFormula(SalaryItem salaryItem) {
        BigDecimal instructorRate = getInstructorRate(salaryItem);
        BigDecimal assistantRate = getAssistantRate(salaryItem);
        return String.format("培训费用 = 讲师小时数×%s元 + 助教小时数×%s元", 
            instructorRate != null ? instructorRate : "讲师费率",
            assistantRate != null ? assistantRate : "助教费率");
    }

    /**
     * 获取讲师费率
     */
    private BigDecimal getInstructorRate(SalaryItem salaryItem) {
        // 优先从薪酬项目配置获取
        if (salaryItem != null && salaryItem.getDefaultValue() != null) {
            return salaryItem.getDefaultValue();
        }
        
        // 默认讲师费率100元/小时
        return BigDecimal.valueOf(100);
    }

    /**
     * 获取助教费率
     */
    private BigDecimal getAssistantRate(SalaryItem salaryItem) {
        // 助教费率通常是讲师费率的一半
        BigDecimal instructorRate = getInstructorRate(salaryItem);
        return instructorRate.multiply(BigDecimal.valueOf(0.5));
    }
}
