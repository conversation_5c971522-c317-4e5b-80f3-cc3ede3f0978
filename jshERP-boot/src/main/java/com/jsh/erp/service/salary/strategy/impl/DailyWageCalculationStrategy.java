package com.jsh.erp.service.salary.strategy.impl;

import com.jsh.erp.datasource.entities.SalaryItem;
import com.jsh.erp.service.salary.dto.EmployeeData;
import com.jsh.erp.service.salary.dto.SalaryCalculationResult;
import com.jsh.erp.service.salary.strategy.SalaryCalculationStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 基础日薪计算策略
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Component
public class DailyWageCalculationStrategy implements SalaryCalculationStrategy {

    private Logger logger = LoggerFactory.getLogger(DailyWageCalculationStrategy.class);

    @Override
    public String getSupportedItemCode() {
        return "DAILY_WAGE";
    }

    @Override
    public String getSupportedItemType() {
        return "FIXED";
    }

    @Override
    public String getStrategyName() {
        return "基础日薪计算";
    }

    @Override
    public String getStrategyDescription() {
        return "根据员工基础日薪和实际工作天数计算基础薪酬";
    }

    @Override
    public SalaryCalculationResult calculate(EmployeeData employeeData, SalaryItem salaryItem, 
                                           String calculationMonth, Long tenantId) throws Exception {
        try {
            // 验证计算条件
            if (!validateConditions(employeeData, salaryItem)) {
                return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                    "计算条件不满足：缺少基础日薪或工作天数数据");
            }

            // 获取基础数据
            BigDecimal dailyWage = employeeData.getDailyWage();
            Map<String, Object> attendanceData = employeeData.getAttendanceData();
            Integer actualWorkDays = (Integer) attendanceData.get("actualWorkDays");
            
            if (actualWorkDays == null) {
                actualWorkDays = (Integer) attendanceData.get("workDays");
            }
            
            if (actualWorkDays == null || actualWorkDays <= 0) {
                return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                    "工作天数数据异常");
            }

            // 计算基础薪酬
            BigDecimal calculatedAmount = dailyWage.multiply(BigDecimal.valueOf(actualWorkDays));
            
            // 创建计算结果
            SalaryCalculationResult result = SalaryCalculationResult.success(
                getSupportedItemCode(), getStrategyName(), calculatedAmount);
            
            result.setItemType("FIXED");
            result.setBaseAmount(dailyWage);
            result.setCalculatedAmount(calculatedAmount);
            result.setFinalAmount(calculatedAmount);
            result.setCalculationFormula(getCalculationFormula(salaryItem));
            result.setRemark(String.format("日薪%s元 × %d天", dailyWage, actualWorkDays));
            
            // 设置计算详情
            Map<String, Object> details = new HashMap<>();
            details.put("dailyWage", dailyWage);
            details.put("actualWorkDays", actualWorkDays);
            details.put("calculationMonth", calculationMonth);
            result.setDetails(details);
            
            logger.debug("员工{}基础日薪计算完成：{}元", employeeData.getEmployeeId(), calculatedAmount);
            
            return result;
            
        } catch (Exception e) {
            logger.error("员工{}基础日薪计算失败", employeeData.getEmployeeId(), e);
            return SalaryCalculationResult.failure(getSupportedItemCode(), getStrategyName(), 
                "计算过程发生异常：" + e.getMessage());
        }
    }

    @Override
    public boolean validateConditions(EmployeeData employeeData, SalaryItem salaryItem) {
        // 检查基础日薪
        if (employeeData.getDailyWage() == null || 
            employeeData.getDailyWage().compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("员工{}基础日薪未设置或为零", employeeData.getEmployeeId());
            return false;
        }
        
        // 检查考勤数据
        Map<String, Object> attendanceData = employeeData.getAttendanceData();
        if (attendanceData == null) {
            logger.warn("员工{}考勤数据缺失", employeeData.getEmployeeId());
            return false;
        }
        
        Integer workDays = (Integer) attendanceData.get("workDays");
        Integer actualWorkDays = (Integer) attendanceData.get("actualWorkDays");
        
        if ((workDays == null || workDays <= 0) && (actualWorkDays == null || actualWorkDays <= 0)) {
            logger.warn("员工{}工作天数数据异常", employeeData.getEmployeeId());
            return false;
        }
        
        return true;
    }

    @Override
    public String getCalculationFormula(SalaryItem salaryItem) {
        return "基础薪酬 = 日薪 × 实际工作天数";
    }
}
