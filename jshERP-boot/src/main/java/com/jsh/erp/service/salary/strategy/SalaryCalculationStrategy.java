package com.jsh.erp.service.salary.strategy;

import com.jsh.erp.service.salary.dto.EmployeeData;
import com.jsh.erp.service.salary.dto.SalaryCalculationResult;
import com.jsh.erp.datasource.entities.SalaryItem;

import java.util.Date;

/**
 * 薪酬计算策略接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface SalaryCalculationStrategy {

    /**
     * 计算薪酬
     * 
     * @param employeeData 员工数据
     * @param salaryItem 薪酬项目
     * @param calculationMonth 计算月份
     * @param tenantId 租户ID
     * @return 计算结果
     */
    SalaryCalculationResult calculate(EmployeeData employeeData, 
                                    SalaryItem salaryItem, 
                                    String calculationMonth,
                                    Long tenantId) throws Exception;

    /**
     * 获取策略支持的薪酬项目类型
     * 
     * @return 薪酬项目类型
     */
    String getSupportedItemType();

    /**
     * 获取策略支持的薪酬项目编码
     *
     * @return 薪酬项目编码
     */
    String getSupportedItemCode();

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取策略描述
     *
     * @return 策略描述
     */
    String getStrategyDescription();

    /**
     * 验证计算条件
     *
     * @param employeeData 员工数据
     * @param salaryItem 薪酬项目
     * @return 是否满足计算条件
     */
    boolean validateConditions(EmployeeData employeeData, SalaryItem salaryItem);

    /**
     * 获取计算公式说明
     *
     * @param salaryItem 薪酬项目
     * @return 公式说明
     */
    String getCalculationFormula(SalaryItem salaryItem);

    /**
     * 验证计算参数
     *
     * @param employeeData 员工数据
     * @param salaryItem 薪酬项目
     * @param calculationMonth 计算月份
     * @return 是否有效
     */
    default boolean validateParameters(EmployeeData employeeData,
                                     SalaryItem salaryItem,
                                     String calculationMonth) {
        return employeeData != null &&
               salaryItem != null &&
               calculationMonth != null &&
               calculationMonth.matches("\\d{4}-\\d{2}");
    }
}
