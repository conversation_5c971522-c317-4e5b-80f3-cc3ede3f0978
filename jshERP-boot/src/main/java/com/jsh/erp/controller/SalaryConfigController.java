package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.datasource.entities.ReimbursementType;
import com.jsh.erp.service.salary.SalaryConfigService;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 薪酬配置管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping(value = "/salary/config")
@Api(tags = {"薪酬配置管理"})
public class SalaryConfigController extends BaseController {

    @Resource
    private SalaryConfigService salaryConfigService;

    // ========== 薪酬项目模板管理 ==========

    /**
     * 获取薪酬项目模板列表
     */
    @GetMapping(value = "/itemTemplate/list")
    @ApiOperation(value = "获取薪酬项目模板列表")
    public TableDataInfo getItemTemplateList(@RequestParam(value = "search", required = false) String search,
                                            HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String itemType = StringUtil.getInfo(search, "itemType");
        String status = StringUtil.getInfo(search, "status");
        
        List<Map<String, Object>> list = salaryConfigService.getItemTemplateList(itemType, status, request);
        return getDataTable(list);
    }

    /**
     * 保存薪酬项目模板
     */
    @PostMapping(value = "/itemTemplate/save")
    @ApiOperation(value = "保存薪酬项目模板")
    public String saveItemTemplate(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveItemTemplate(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 删除薪酬项目模板
     */
    @DeleteMapping(value = "/itemTemplate/delete")
    @ApiOperation(value = "删除薪酬项目模板")
    public String deleteItemTemplate(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.deleteItemTemplate(id, request);
        if (result > 0) {
            return returnJson(objectMap, "删除成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "删除失败", ErpInfo.ERROR.code);
        }
    }

    // ========== 报销类型管理 ==========

    /**
     * 获取报销类型列表
     */
    @GetMapping(value = "/reimbursementType/list")
    @ApiOperation(value = "获取报销类型列表")
    public TableDataInfo getReimbursementTypeList(@RequestParam(value = "search", required = false) String search,
                                                 HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String status = StringUtil.getInfo(search, "status");
        
        List<ReimbursementType> list = salaryConfigService.getReimbursementTypeList(status, request);
        return getDataTable(list);
    }

    /**
     * 保存报销类型
     */
    @PostMapping(value = "/reimbursementType/save")
    @ApiOperation(value = "保存报销类型")
    public String saveReimbursementType(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveReimbursementType(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    // ========== 计算引擎配置 ==========

    /**
     * 获取计算引擎配置
     */
    @GetMapping(value = "/calculationEngine")
    @ApiOperation(value = "获取计算引擎配置")
    public String getCalculationEngineConfig(HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> config = salaryConfigService.getCalculationEngineConfig(request);
        objectMap.put("config", config);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 保存计算引擎配置
     */
    @PostMapping(value = "/calculationEngine/save")
    @ApiOperation(value = "保存计算引擎配置")
    public String saveCalculationEngineConfig(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveCalculationEngineConfig(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    // ========== 数据匹配逻辑配置 ==========

    /**
     * 获取数据匹配逻辑配置
     */
    @GetMapping(value = "/matchingLogic")
    @ApiOperation(value = "获取数据匹配逻辑配置")
    public String getMatchingLogicConfig(HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> config = salaryConfigService.getMatchingLogicConfig(request);
        objectMap.put("config", config);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 保存数据匹配逻辑配置
     */
    @PostMapping(value = "/matchingLogic/save")
    @ApiOperation(value = "保存数据匹配逻辑配置")
    public String saveMatchingLogicConfig(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveMatchingLogicConfig(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    // ========== 审批流程配置 ==========

    /**
     * 获取审批流程配置
     */
    @GetMapping(value = "/approvalProcess")
    @ApiOperation(value = "获取审批流程配置")
    public String getApprovalProcessConfig(@RequestParam("processType") String processType,
                                          HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> config = salaryConfigService.getApprovalProcessConfig(processType, request);
        objectMap.put("config", config);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 保存审批流程配置
     */
    @PostMapping(value = "/approvalProcess/save")
    @ApiOperation(value = "保存审批流程配置")
    public String saveApprovalProcessConfig(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveApprovalProcessConfig(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    // ========== 默认设置 ==========

    /**
     * 获取默认设置
     */
    @GetMapping(value = "/defaultSettings")
    @ApiOperation(value = "获取默认设置")
    public String getDefaultSettings(HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> settings = salaryConfigService.getDefaultSettings(request);
        objectMap.put("settings", settings);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 保存默认设置
     */
    @PostMapping(value = "/defaultSettings/save")
    @ApiOperation(value = "保存默认设置")
    public String saveDefaultSettings(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.saveDefaultSettings(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "保存成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "保存失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 批量配置员工薪酬
     */
    @PostMapping(value = "/batchConfig")
    @ApiOperation(value = "批量配置员工薪酬")
    public String batchConfigSalary(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = salaryConfigService.batchConfigSalary(obj, request);
        if (result > 0) {
            return returnJson(objectMap, "批量配置成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "批量配置失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 统一返回JSON格式
     */
    private String returnJson(Map<String, Object> objectMap, String message, int code) {
        JSONObject result = new JSONObject();
        result.put("message", message);
        result.put("code", code);
        result.put("data", objectMap);
        result.put("success", code == ErpInfo.OK.code);
        return result.toString();
    }
}
