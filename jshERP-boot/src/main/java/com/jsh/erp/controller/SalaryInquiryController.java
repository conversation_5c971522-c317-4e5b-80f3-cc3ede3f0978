package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.service.salary.SalaryInquiryService;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 薪酬查询控制器
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping(value = "/salary/inquiry")
@Api(tags = {"薪酬查询"})
public class SalaryInquiryController extends BaseController {

    @Resource
    private SalaryInquiryService salaryInquiryService;

    /**
     * 获取个人薪酬记录
     */
    @GetMapping(value = "/personal")
    @ApiOperation(value = "获取个人薪酬记录")
    public TableDataInfo getPersonalSalary(@RequestParam(value = "search", required = false) String search,
                                          HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String calculationMonth = StringUtil.getInfo(search, "calculationMonth");
        String year = StringUtil.getInfo(search, "year");
        
        List<Map<String, Object>> list = salaryInquiryService.getPersonalSalaryRecords(calculationMonth, year, request);
        return getDataTable(list);
    }

    /**
     * 获取个人薪酬详情
     */
    @GetMapping(value = "/personalDetail")
    @ApiOperation(value = "获取个人薪酬详情")
    public String getPersonalSalaryDetail(@RequestParam("calculationId") Long calculationId,
                                         HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> detail = salaryInquiryService.getPersonalSalaryDetail(calculationId, request);
        if (detail != null) {
            objectMap.put("info", detail);
            return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "数据不存在", ErpInfo.ERROR.code);
        }
    }

    /**
     * 获取薪酬统计信息
     */
    @GetMapping(value = "/statistics")
    @ApiOperation(value = "获取薪酬统计信息")
    public String getSalaryStatistics(@RequestParam(value = "search", required = false) String search,
                                     HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        String year = StringUtil.getInfo(search, "year");
        String department = StringUtil.getInfo(search, "department");
        String statisticsType = StringUtil.getInfo(search, "statisticsType"); // MONTHLY, YEARLY, DEPARTMENT
        
        Map<String, Object> statistics = salaryInquiryService.getSalaryStatistics(year, department, statisticsType, request);
        objectMap.put("statistics", statistics);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 获取薪酬趋势分析
     */
    @GetMapping(value = "/trend")
    @ApiOperation(value = "获取薪酬趋势分析")
    public String getSalaryTrend(@RequestParam(value = "search", required = false) String search,
                                HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        String startMonth = StringUtil.getInfo(search, "startMonth");
        String endMonth = StringUtil.getInfo(search, "endMonth");
        String employeeId = StringUtil.getInfo(search, "employeeId");
        
        List<Map<String, Object>> trendData = salaryInquiryService.getSalaryTrend(startMonth, endMonth, employeeId, request);
        objectMap.put("trendData", trendData);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 获取部门薪酬对比
     */
    @GetMapping(value = "/departmentComparison")
    @ApiOperation(value = "获取部门薪酬对比")
    public String getDepartmentComparison(@RequestParam(value = "search", required = false) String search,
                                         HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        String calculationMonth = StringUtil.getInfo(search, "calculationMonth");
        
        List<Map<String, Object>> comparisonData = salaryInquiryService.getDepartmentComparison(calculationMonth, request);
        objectMap.put("comparisonData", comparisonData);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 获取薪酬构成分析
     */
    @GetMapping(value = "/composition")
    @ApiOperation(value = "获取薪酬构成分析")
    public String getSalaryComposition(@RequestParam("calculationId") Long calculationId,
                                      HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Map<String, Object> composition = salaryInquiryService.getSalaryComposition(calculationId, request);
        objectMap.put("composition", composition);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 导出个人薪酬记录
     */
    @PostMapping(value = "/exportPersonal")
    @ApiOperation(value = "导出个人薪酬记录")
    public String exportPersonalSalary(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        String startMonth = obj.getString("startMonth");
        String endMonth = obj.getString("endMonth");
        String format = obj.getString("format"); // PDF, EXCEL
        
        String fileUrl = salaryInquiryService.exportPersonalSalary(startMonth, endMonth, format, request);
        objectMap.put("fileUrl", fileUrl);
        
        return returnJson(objectMap, "导出成功", ErpInfo.OK.code);
    }

    /**
     * 获取薪酬排行榜
     */
    @GetMapping(value = "/ranking")
    @ApiOperation(value = "获取薪酬排行榜")
    public TableDataInfo getSalaryRanking(@RequestParam(value = "search", required = false) String search,
                                         HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String calculationMonth = StringUtil.getInfo(search, "calculationMonth");
        String department = StringUtil.getInfo(search, "department");
        String rankingType = StringUtil.getInfo(search, "rankingType"); // TOTAL, FIXED, COMMISSION
        
        List<Map<String, Object>> list = salaryInquiryService.getSalaryRanking(calculationMonth, department, rankingType, request);
        return getDataTable(list);
    }

    /**
     * 获取薪酬汇总报表
     */
    @GetMapping(value = "/summary")
    @ApiOperation(value = "获取薪酬汇总报表")
    public String getSalarySummary(@RequestParam(value = "search", required = false) String search,
                                  HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        String calculationMonth = StringUtil.getInfo(search, "calculationMonth");
        String groupBy = StringUtil.getInfo(search, "groupBy"); // DEPARTMENT, POSITION, EMPLOYEE
        
        List<Map<String, Object>> summaryData = salaryInquiryService.getSalarySummary(calculationMonth, groupBy, request);
        objectMap.put("summaryData", summaryData);
        
        return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
    }

    /**
     * 统一返回JSON格式
     */
    private String returnJson(Map<String, Object> objectMap, String message, int code) {
        JSONObject result = new JSONObject();
        result.put("message", message);
        result.put("code", code);
        result.put("data", objectMap);
        result.put("success", code == ErpInfo.OK.code);
        return result.toString();
    }
}
