package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.base.BaseController;
import com.jsh.erp.base.TableDataInfo;
import com.jsh.erp.datasource.entities.Reimbursement;
import com.jsh.erp.service.salary.ReimbursementService;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报销管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@RequestMapping(value = "/salary/reimbursement")
@Api(tags = {"报销管理"})
public class ReimbursementController extends BaseController {

    @Resource
    private ReimbursementService reimbursementService;

    /**
     * 获取报销申请列表
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "获取报销申请列表")
    public TableDataInfo getList(@RequestParam(value = "search", required = false) String search,
                                HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String employeeName = StringUtil.getInfo(search, "employeeName");
        String department = StringUtil.getInfo(search, "department");
        String reimbursementType = StringUtil.getInfo(search, "reimbursementType");
        String status = StringUtil.getInfo(search, "status");
        String applyDateStart = StringUtil.getInfo(search, "applyDateStart");
        String applyDateEnd = StringUtil.getInfo(search, "applyDateEnd");
        
        List<Reimbursement> list = reimbursementService.select(employeeName, department, 
                                                              reimbursementType, status, 
                                                              applyDateStart, applyDateEnd, request);
        return getDataTable(list);
    }

    /**
     * 根据ID获取报销申请详情
     */
    @GetMapping(value = "/info")
    @ApiOperation(value = "根据ID获取报销申请详情")
    public String getInfo(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        Reimbursement reimbursement = reimbursementService.getReimbursementWithItems(id, request);
        if (reimbursement != null) {
            objectMap.put("info", reimbursement);
            return returnJson(objectMap, "查询成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "数据不存在", ErpInfo.ERROR.code);
        }
    }

    /**
     * 新增报销申请
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增报销申请")
    public String add(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = reimbursementService.insertReimbursement(obj, request);
        
        if (result > 0) {
            return returnJson(objectMap, "新增成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "新增失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 更新报销申请
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新报销申请")
    public String update(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = reimbursementService.updateReimbursement(obj, request);
        
        if (result > 0) {
            return returnJson(objectMap, "更新成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "更新失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 删除报销申请
     */
    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除报销申请")
    public String delete(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = reimbursementService.deleteReimbursement(id, request);
        if (result > 0) {
            return returnJson(objectMap, "删除成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "删除失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 批量删除报销申请
     */
    @DeleteMapping(value = "/deleteBatch")
    @ApiOperation(value = "批量删除报销申请")
    public String deleteBatch(@RequestParam("ids") String ids, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = reimbursementService.batchDeleteReimbursement(ids, request);
        if (result > 0) {
            return returnJson(objectMap, "批量删除成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "批量删除失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 提交报销申请
     */
    @PutMapping(value = "/submit")
    @ApiOperation(value = "提交报销申请")
    public String submit(@RequestParam("id") Long id, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        int result = reimbursementService.submitReimbursement(id, request);
        if (result > 0) {
            return returnJson(objectMap, "提交成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "提交失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 审批报销申请
     */
    @PutMapping(value = "/approve")
    @ApiOperation(value = "审批报销申请")
    public String approve(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Long id = obj.getLong("id");
        String action = obj.getString("action"); // APPROVE, REJECT
        String remark = obj.getString("remark");
        
        int result = reimbursementService.approveReimbursement(id, action, remark, request);
        if (result > 0) {
            String message = "APPROVE".equals(action) ? "审批通过" : "审批拒绝";
            return returnJson(objectMap, message, ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "审批失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 支付报销申请
     */
    @PutMapping(value = "/pay")
    @ApiOperation(value = "支付报销申请")
    public String pay(@RequestBody JSONObject obj, HttpServletRequest request) throws Exception {
        Map<String, Object> objectMap = new HashMap<>();
        
        Long id = obj.getLong("id");
        String paymentMethod = obj.getString("paymentMethod");
        String paymentAccount = obj.getString("paymentAccount");
        
        int result = reimbursementService.payReimbursement(id, paymentMethod, paymentAccount, request);
        if (result > 0) {
            return returnJson(objectMap, "支付成功", ErpInfo.OK.code);
        } else {
            return returnJson(objectMap, "支付失败", ErpInfo.ERROR.code);
        }
    }

    /**
     * 获取我的报销申请列表
     */
    @GetMapping(value = "/myList")
    @ApiOperation(value = "获取我的报销申请列表")
    public TableDataInfo getMyList(@RequestParam(value = "search", required = false) String search,
                                  HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        String status = StringUtil.getInfo(search, "status");
        String applyDateStart = StringUtil.getInfo(search, "applyDateStart");
        String applyDateEnd = StringUtil.getInfo(search, "applyDateEnd");
        
        List<Reimbursement> list = reimbursementService.selectMyReimbursements(status, 
                                                                              applyDateStart, 
                                                                              applyDateEnd, request);
        return getDataTable(list);
    }

    /**
     * 获取待我审批的报销申请列表
     */
    @GetMapping(value = "/pendingApproval")
    @ApiOperation(value = "获取待我审批的报销申请列表")
    public TableDataInfo getPendingApproval(@RequestParam(value = "search", required = false) String search,
                                           HttpServletRequest request) throws Exception {
        PageUtils.startPage();
        
        List<Reimbursement> list = reimbursementService.selectPendingApprovals(request);
        return getDataTable(list);
    }

    /**
     * 统一返回JSON格式
     */
    private String returnJson(Map<String, Object> objectMap, String message, int code) {
        JSONObject result = new JSONObject();
        result.put("message", message);
        result.put("code", code);
        result.put("data", objectMap);
        result.put("success", code == ErpInfo.OK.code);
        return result.toString();
    }
}
