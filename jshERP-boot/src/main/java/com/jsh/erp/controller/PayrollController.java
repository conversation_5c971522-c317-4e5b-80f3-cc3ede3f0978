package com.jsh.erp.controller;

import com.jsh.erp.base.BaseController;
import com.jsh.erp.service.PayrollService;
import com.jsh.erp.utils.BaseResponseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 薪酬管理控制器
 */
@RestController
@RequestMapping(value = "/payroll")
@Api(tags = "薪酬管理")
public class PayrollController extends BaseController {

    @Resource
    private PayrollService payrollService;

    /**
     * 生成工资单
     */
    @PostMapping("/generatePayslip")
    @ApiOperation(value = "生成工资单")
    public BaseResponseInfo generatePayslip(@RequestParam Long employeeId,
                                           @RequestParam String payPeriod,
                                           HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            Long tenantId = getTenantId(request);
            Long userId = getUserId(request);
            
            Long payslipId = payrollService.generatePayslip(employeeId, payPeriod, tenantId, userId);
            
            res.code = 200;
            res.data = payslipId;
            res.message = "工资单生成成功";
        } catch (Exception e) {
            res.code = 500;
            res.message = e.getMessage();
        }
        return res;
    }

    /**
     * 测试生成龚锦华的工资单
     */
    @PostMapping("/testGeneratePayslip")
    @ApiOperation(value = "测试生成工资单")
    public BaseResponseInfo testGeneratePayslip(HttpServletRequest request) throws Exception {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            // 使用龚锦华的员工ID和2024-12月份
            Long employeeId = 147L;
            String payPeriod = "2024-12";
            Long tenantId = 63L;
            Long userId = 63L;
            
            Long payslipId = payrollService.generatePayslip(employeeId, payPeriod, tenantId, userId);
            
            res.code = 200;
            res.data = payslipId;
            res.message = "龚锦华2024年12月工资单生成成功，工资单ID: " + payslipId;
        } catch (Exception e) {
            res.code = 500;
            res.message = "生成失败: " + e.getMessage();
        }
        return res;
    }
}
