package com.jsh.erp.controller;

import com.alibaba.fastjson.JSONObject;
import com.jsh.erp.constants.BusinessConstants;
import com.jsh.erp.datasource.entities.CloisonneDuty;
import com.jsh.erp.datasource.entities.Person;
import com.jsh.erp.service.cloisonne.CloisonneDutyService;
import com.jsh.erp.utils.BaseResponseInfo;
import com.jsh.erp.utils.ErpInfo;
import com.jsh.erp.utils.PageUtils;
import com.jsh.erp.utils.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 掐丝珐琅馆值班管理控制器
 */
@RestController
@RequestMapping(value = "/cloisonne/duty")
@Api(tags = {"掐丝珐琅馆值班管理"})
public class CloisonneDutyController {

    private Logger logger = LoggerFactory.getLogger(CloisonneDutyController.class);

    @Resource
    private CloisonneDutyService cloisonneDutyService;

    /**
     * 查询值班记录列表
     */
    @GetMapping(value = "/list")
    @ApiOperation(value = "查询值班记录列表")
    public TableDataInfo list(@RequestParam(value = "search", required = false) String search,
                              @RequestParam(value = "currentPage", required = false) Integer currentPage,
                              @RequestParam(value = "pageSize", required = false) Integer pageSize,
                              HttpServletRequest request) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        try {
            List<CloisonneDuty> dataList = cloisonneDutyService.select(search, currentPage, pageSize);
            tableDataInfo.setRows(dataList);
            tableDataInfo.setTotal(Long.valueOf(dataList.size()));
        } catch (Exception e) {
            logger.error("查询值班记录列表失败", e);
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("查询失败");
        }
        return tableDataInfo;
    }

    /**
     * 根据年月查询值班记录
     */
    @GetMapping(value = "/listByYearMonth")
    @ApiOperation(value = "根据年月查询值班记录")
    public BaseResponseInfo listByYearMonth(@RequestParam("year") Integer year,
                                            @RequestParam("month") Integer month,
                                            HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            List<CloisonneDuty> dataList = cloisonneDutyService.selectByYearMonth(year, month);
            res.code = 200;
            res.data = dataList;
        } catch (Exception e) {
            logger.error("根据年月查询值班记录失败", e);
            res.code = 500;
            res.data = "查询失败";
        }
        return res;
    }

    /**
     * 根据ID查询值班记录详情
     */
    @GetMapping(value = "/queryById")
    @ApiOperation(value = "根据ID查询值班记录详情")
    public BaseResponseInfo queryById(@RequestParam("id") Long id, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            CloisonneDuty cloisonneDuty = cloisonneDutyService.getCloisonneDuty(id);
            res.code = 200;
            res.data = cloisonneDuty;
        } catch (Exception e) {
            logger.error("查询值班记录详情失败", e);
            res.code = 500;
            res.data = "查询失败";
        }
        return res;
    }

    /**
     * 新增值班记录
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增值班记录")
    public BaseResponseInfo addCloisonneDuty(@RequestBody JSONObject obj, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            int result = cloisonneDutyService.insertCloisonneDuty(obj, request);
            if (result > 0) {
                res.code = 200;
                res.data = "新增成功";
            } else {
                res.code = 500;
                res.data = "新增失败";
            }
        } catch (Exception e) {
            logger.error("新增值班记录失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 更新值班记录
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新值班记录")
    public BaseResponseInfo updateCloisonneDuty(@RequestBody JSONObject obj, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            int result = cloisonneDutyService.updateCloisonneDuty(obj, request);
            if (result > 0) {
                res.code = 200;
                res.data = "更新成功";
            } else {
                res.code = 500;
                res.data = "更新失败";
            }
        } catch (Exception e) {
            logger.error("更新值班记录失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 删除值班记录
     */
    @DeleteMapping(value = "/delete")
    @ApiOperation(value = "删除值班记录")
    public BaseResponseInfo deleteCloisonneDuty(@RequestParam("id") Long id, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            int result = cloisonneDutyService.deleteCloisonneDuty(id, request);
            if (result > 0) {
                res.code = 200;
                res.data = "删除成功";
            } else {
                res.code = 500;
                res.data = "删除失败";
            }
        } catch (Exception e) {
            logger.error("删除值班记录失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 批量删除值班记录
     */
    @DeleteMapping(value = "/deleteBatch")
    @ApiOperation(value = "批量删除值班记录")
    public BaseResponseInfo deleteBatch(@RequestParam("ids") String ids, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            int result = cloisonneDutyService.batchDeleteCloisonneDuty(ids, request);
            if (result > 0) {
                res.code = 200;
                res.data = "批量删除成功";
            } else {
                res.code = 500;
                res.data = "批量删除失败";
            }
        } catch (Exception e) {
            logger.error("批量删除值班记录失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 批量新增值班记录
     */
    @PostMapping(value = "/batchAdd")
    @ApiOperation(value = "批量新增值班记录")
    public BaseResponseInfo batchAdd(@RequestBody JSONObject obj, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            int result = cloisonneDutyService.batchInsertCloisonneDuty(obj, request);
            if (result > 0) {
                res.code = 200;
                res.data = "批量新增成功，共添加 " + result + " 条记录";
            } else {
                res.code = 500;
                res.data = "批量新增失败";
            }
        } catch (Exception e) {
            logger.error("批量新增值班记录失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 检查值班冲突
     */
    @PostMapping(value = "/checkConflict")
    @ApiOperation(value = "检查值班冲突")
    public BaseResponseInfo checkConflict(@RequestBody JSONObject obj, HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            // 这里可以调用Service层的冲突检查方法
            // 暂时返回无冲突
            Map<String, Object> result = new HashMap<>();
            result.put("hasConflict", false);
            result.put("message", "无冲突");
            
            res.code = 200;
            res.data = result;
        } catch (Exception e) {
            logger.error("检查值班冲突失败", e);
            res.code = 500;
            res.data = e.getMessage();
        }
        return res;
    }

    /**
     * 获取员工列表
     */
    @GetMapping(value = "/employees")
    @ApiOperation(value = "获取员工列表")
    public BaseResponseInfo getEmployees(HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            List<Person> employees = cloisonneDutyService.getEmployeeList();
            res.code = 200;
            res.data = employees;
        } catch (Exception e) {
            logger.error("获取员工列表失败", e);
            res.code = 500;
            res.data = "获取员工列表失败";
        }
        return res;
    }

    /**
     * 获取值班统计数据
     */
    @GetMapping(value = "/statistics")
    @ApiOperation(value = "获取值班统计数据")
    public BaseResponseInfo getStatistics(@RequestParam(value = "startDate", required = false) String startDate,
                                          @RequestParam(value = "endDate", required = false) String endDate,
                                          HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            Map<String, Object> statistics = cloisonneDutyService.getStatistics(startDate, endDate);
            res.code = 200;
            res.data = statistics;
        } catch (Exception e) {
            logger.error("获取值班统计数据失败", e);
            res.code = 500;
            res.data = "获取统计数据失败";
        }
        return res;
    }

    /**
     * 获取班次类型列表
     */
    @GetMapping(value = "/shiftTypes")
    @ApiOperation(value = "获取班次类型列表")
    public BaseResponseInfo getShiftTypes(HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            // 返回预定义的班次类型
            String[] shiftTypes = {"早班", "晚班", "全天"};
            res.code = 200;
            res.data = shiftTypes;
        } catch (Exception e) {
            logger.error("获取班次类型列表失败", e);
            res.code = 500;
            res.data = "获取班次类型失败";
        }
        return res;
    }

    /**
     * 获取值班日历数据
     */
    @GetMapping(value = "/calendar")
    @ApiOperation(value = "获取值班日历数据")
    public BaseResponseInfo getCalendar(@RequestParam("yearMonth") String yearMonth,
                                        HttpServletRequest request) {
        BaseResponseInfo res = new BaseResponseInfo();
        try {
            String[] parts = yearMonth.split("-");
            Integer year = Integer.parseInt(parts[0]);
            Integer month = Integer.parseInt(parts[1]);
            
            List<CloisonneDuty> dataList = cloisonneDutyService.selectByYearMonth(year, month);
            res.code = 200;
            res.data = dataList;
        } catch (Exception e) {
            logger.error("获取值班日历数据失败", e);
            res.code = 500;
            res.data = "获取日历数据失败";
        }
        return res;
    }
}
