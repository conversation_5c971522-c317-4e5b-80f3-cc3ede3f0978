package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.TimesheetData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 考勤数据Mapper接口
 */
public interface TimesheetDataMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(TimesheetData record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(TimesheetData record);

    /**
     * 根据主键查询
     */
    TimesheetData selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(TimesheetData record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(TimesheetData record);

    /**
     * 根据员工ID和日期范围查询考勤数据
     */
    List<TimesheetData> selectByEmployeeAndPeriod(@Param("employeeId") Long employeeId,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate,
                                                 @Param("tenantId") Long tenantId);

    /**
     * 根据条件查询列表
     */
    List<TimesheetData> selectByCondition(@Param("employeeName") String employeeName,
                                        @Param("workDate") Date workDate,
                                        @Param("attendanceStatus") String attendanceStatus,
                                        @Param("tenantId") Long tenantId,
                                        @Param("offset") Integer offset,
                                        @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("employeeName") String employeeName,
                         @Param("workDate") Date workDate,
                         @Param("attendanceStatus") String attendanceStatus,
                         @Param("tenantId") Long tenantId);

    /**
     * 根据员工ID和工作日期查询
     */
    TimesheetData selectByEmployeeAndDate(@Param("employeeId") Long employeeId,
                                        @Param("workDate") Date workDate,
                                        @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);
}
