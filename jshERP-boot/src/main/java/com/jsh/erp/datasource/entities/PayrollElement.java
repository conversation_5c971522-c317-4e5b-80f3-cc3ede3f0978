package com.jsh.erp.datasource.entities;

import java.util.Date;

/**
 * 薪酬项目实体类
 */
public class PayrollElement {
    private Long id;
    private String elementCode;
    private String elementName;
    private String elementType;
    private String calculationRule;
    private String formulaExpression;
    private String taxAttribute;
    private String isMandatory;
    private Integer displayOrder;
    private String status;
    private String remark;
    private Long tenantId;
    private String deleteFlag;
    private Date createTime;
    private Long creator;
    private Date updateTime;
    private Long updater;

    public PayrollElement() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getElementCode() {
        return elementCode;
    }

    public void setElementCode(String elementCode) {
        this.elementCode = elementCode;
    }

    public String getElementName() {
        return elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    public String getElementType() {
        return elementType;
    }

    public void setElementType(String elementType) {
        this.elementType = elementType;
    }

    public String getCalculationRule() {
        return calculationRule;
    }

    public void setCalculationRule(String calculationRule) {
        this.calculationRule = calculationRule;
    }

    public String getFormulaExpression() {
        return formulaExpression;
    }

    public void setFormulaExpression(String formulaExpression) {
        this.formulaExpression = formulaExpression;
    }

    public String getTaxAttribute() {
        return taxAttribute;
    }

    public void setTaxAttribute(String taxAttribute) {
        this.taxAttribute = taxAttribute;
    }

    public String getIsMandatory() {
        return isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdater() {
        return updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    @Override
    public String toString() {
        return "PayrollElement{" +
                "id=" + id +
                ", elementCode='" + elementCode + '\'' +
                ", elementName='" + elementName + '\'' +
                ", elementType='" + elementType + '\'' +
                ", calculationRule='" + calculationRule + '\'' +
                ", formulaExpression='" + formulaExpression + '\'' +
                ", taxAttribute='" + taxAttribute + '\'' +
                ", isMandatory='" + isMandatory + '\'' +
                ", displayOrder=" + displayOrder +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                ", tenantId=" + tenantId +
                ", deleteFlag='" + deleteFlag + '\'' +
                ", createTime=" + createTime +
                ", creator=" + creator +
                ", updateTime=" + updateTime +
                ", updater=" + updater +
                '}';
    }
}
