package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.Reimbursement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报销申请扩展Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Mapper
public interface ReimbursementMapperEx {

    /**
     * 根据条件查询报销申请列表
     */
    List<Reimbursement> selectByCondition(@Param("employeeName") String employeeName,
                                         @Param("department") String department,
                                         @Param("reimbursementType") String reimbursementType,
                                         @Param("status") String status,
                                         @Param("applyDateStart") String applyDateStart,
                                         @Param("applyDateEnd") String applyDateEnd,
                                         @Param("tenantId") Long tenantId);

    /**
     * 获取报销申请详情（包含明细）
     */
    Reimbursement selectWithItems(@Param("id") Long id, @Param("tenantId") Long tenantId);

    /**
     * 获取我的报销申请列表
     */
    List<Reimbursement> selectMyReimbursements(@Param("employeeId") Long employeeId,
                                              @Param("status") String status,
                                              @Param("applyDateStart") String applyDateStart,
                                              @Param("applyDateEnd") String applyDateEnd,
                                              @Param("tenantId") Long tenantId);

    /**
     * 获取待我审批的报销申请列表
     */
    List<Reimbursement> selectPendingApprovals(@Param("currentApprover") String currentApprover,
                                              @Param("tenantId") Long tenantId);

    /**
     * 统计报销金额
     */
    List<Reimbursement> selectReimbursementSummary(@Param("employeeId") Long employeeId,
                                                  @Param("month") String month,
                                                  @Param("tenantId") Long tenantId);
}
