package com.jsh.erp.datasource.entities;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 员工薪酬档案实体类
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@TableName("jsh_salary_profile")
public class SalaryProfile {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("employee_id")
    private Long employeeId;

    @TableField("employee_name")
    private String employeeName;

    @TableField("id_card")
    private String idCard;

    @TableField("phone")
    private String phone;

    @TableField("email")
    private String email;

    @TableField("department")
    private String department;

    @TableField("position")
    private String position;

    @TableField("entry_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    @TableField("profile_name")
    private String profileName;

    @TableField("daily_wage")
    private BigDecimal dailyWage;

    @TableField("daily_wage_enabled")
    private Boolean dailyWageEnabled;

    @TableField("coffee_commission_enabled")
    private Boolean coffeeCommissionEnabled;

    @TableField("coffee_commission_rate")
    private BigDecimal coffeeCommissionRate;

    @TableField("hall_sales_commission_enabled")
    private Boolean hallSalesCommissionEnabled;

    @TableField("hall_sales_commission_rate")
    private BigDecimal hallSalesCommissionRate;

    @TableField("channel_commission_enabled")
    private Boolean channelCommissionEnabled;

    @TableField("channel_commission_rate")
    private BigDecimal channelCommissionRate;

    @TableField("external_instructor_enabled")
    private Boolean externalInstructorEnabled;

    @TableField("external_instructor_amount")
    private BigDecimal externalInstructorAmount;

    @TableField("internal_instructor_enabled")
    private Boolean internalInstructorEnabled;

    @TableField("internal_instructor_amount")
    private BigDecimal internalInstructorAmount;

    @TableField("external_assistant_enabled")
    private Boolean externalAssistantEnabled;

    @TableField("external_assistant_amount")
    private BigDecimal externalAssistantAmount;

    @TableField("internal_assistant_enabled")
    private Boolean internalAssistantEnabled;

    @TableField("internal_assistant_amount")
    private BigDecimal internalAssistantAmount;

    @TableField("artwork_sales_enabled")
    private Boolean artworkSalesEnabled;

    @TableField("status")
    private String status;

    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date effectiveDate;

    @TableField("expire_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

    @TableField("salary_status")
    private String salaryStatus;

    @TableField("remark")
    private String remark;

    @TableField("tenant_id")
    private Long tenantId;

    @TableLogic
    @TableField("delete_flag")
    private String deleteFlag;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField("creator")
    private Long creator;

    @TableField("updater")
    private Long updater;

    // 构造函数
    public SalaryProfile() {
    }

    public SalaryProfile(Long employeeId, String employeeName) {
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.salaryStatus = "ACTIVE";
        this.dailyWage = BigDecimal.ZERO;
        this.deleteFlag = "0";
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    public BigDecimal getDailyWage() {
        return dailyWage;
    }

    public void setDailyWage(BigDecimal dailyWage) {
        this.dailyWage = dailyWage;
    }

    public String getSalaryStatus() {
        return salaryStatus;
    }

    public void setSalaryStatus(String salaryStatus) {
        this.salaryStatus = salaryStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getUpdater() {
        return updater;
    }

    public void setUpdater(Long updater) {
        this.updater = updater;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public Boolean getDailyWageEnabled() {
        return dailyWageEnabled;
    }

    public void setDailyWageEnabled(Boolean dailyWageEnabled) {
        this.dailyWageEnabled = dailyWageEnabled;
    }

    public Boolean getCoffeeCommissionEnabled() {
        return coffeeCommissionEnabled;
    }

    public void setCoffeeCommissionEnabled(Boolean coffeeCommissionEnabled) {
        this.coffeeCommissionEnabled = coffeeCommissionEnabled;
    }

    public BigDecimal getCoffeeCommissionRate() {
        return coffeeCommissionRate;
    }

    public void setCoffeeCommissionRate(BigDecimal coffeeCommissionRate) {
        this.coffeeCommissionRate = coffeeCommissionRate;
    }

    public Boolean getHallSalesCommissionEnabled() {
        return hallSalesCommissionEnabled;
    }

    public void setHallSalesCommissionEnabled(Boolean hallSalesCommissionEnabled) {
        this.hallSalesCommissionEnabled = hallSalesCommissionEnabled;
    }

    public BigDecimal getHallSalesCommissionRate() {
        return hallSalesCommissionRate;
    }

    public void setHallSalesCommissionRate(BigDecimal hallSalesCommissionRate) {
        this.hallSalesCommissionRate = hallSalesCommissionRate;
    }

    public Boolean getChannelCommissionEnabled() {
        return channelCommissionEnabled;
    }

    public void setChannelCommissionEnabled(Boolean channelCommissionEnabled) {
        this.channelCommissionEnabled = channelCommissionEnabled;
    }

    public BigDecimal getChannelCommissionRate() {
        return channelCommissionRate;
    }

    public void setChannelCommissionRate(BigDecimal channelCommissionRate) {
        this.channelCommissionRate = channelCommissionRate;
    }

    public Boolean getExternalInstructorEnabled() {
        return externalInstructorEnabled;
    }

    public void setExternalInstructorEnabled(Boolean externalInstructorEnabled) {
        this.externalInstructorEnabled = externalInstructorEnabled;
    }

    public BigDecimal getExternalInstructorAmount() {
        return externalInstructorAmount;
    }

    public void setExternalInstructorAmount(BigDecimal externalInstructorAmount) {
        this.externalInstructorAmount = externalInstructorAmount;
    }

    public Boolean getInternalInstructorEnabled() {
        return internalInstructorEnabled;
    }

    public void setInternalInstructorEnabled(Boolean internalInstructorEnabled) {
        this.internalInstructorEnabled = internalInstructorEnabled;
    }

    public BigDecimal getInternalInstructorAmount() {
        return internalInstructorAmount;
    }

    public void setInternalInstructorAmount(BigDecimal internalInstructorAmount) {
        this.internalInstructorAmount = internalInstructorAmount;
    }

    public Boolean getExternalAssistantEnabled() {
        return externalAssistantEnabled;
    }

    public void setExternalAssistantEnabled(Boolean externalAssistantEnabled) {
        this.externalAssistantEnabled = externalAssistantEnabled;
    }

    public BigDecimal getExternalAssistantAmount() {
        return externalAssistantAmount;
    }

    public void setExternalAssistantAmount(BigDecimal externalAssistantAmount) {
        this.externalAssistantAmount = externalAssistantAmount;
    }

    public Boolean getInternalAssistantEnabled() {
        return internalAssistantEnabled;
    }

    public void setInternalAssistantEnabled(Boolean internalAssistantEnabled) {
        this.internalAssistantEnabled = internalAssistantEnabled;
    }

    public BigDecimal getInternalAssistantAmount() {
        return internalAssistantAmount;
    }

    public void setInternalAssistantAmount(BigDecimal internalAssistantAmount) {
        this.internalAssistantAmount = internalAssistantAmount;
    }

    public Boolean getArtworkSalesEnabled() {
        return artworkSalesEnabled;
    }

    public void setArtworkSalesEnabled(Boolean artworkSalesEnabled) {
        this.artworkSalesEnabled = artworkSalesEnabled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    @Override
    public String toString() {
        return "SalaryProfile{" +
                "id=" + id +
                ", employeeId=" + employeeId +
                ", employeeName='" + employeeName + '\'' +
                ", department='" + department + '\'' +
                ", position='" + position + '\'' +
                ", dailyWage=" + dailyWage +
                ", salaryStatus='" + salaryStatus + '\'' +
                ", tenantId=" + tenantId +
                '}';
    }
}
