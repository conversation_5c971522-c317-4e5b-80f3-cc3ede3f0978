package com.jsh.erp.datasource.entities;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 报销明细实体类
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@TableName("jsh_reimbursement_item")
public class ReimbursementItem {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("reimbursement_id")
    private Long reimbursementId;

    @TableField("item_type")
    private String itemType;

    @TableField("expense_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expenseDate;

    @TableField("amount")
    private BigDecimal amount;

    @TableField("description")
    private String description;

    @TableField("receipt_url")
    private String receiptUrl;

    @TableField("tenant_id")
    private Long tenantId;

    @TableLogic
    @TableField("delete_flag")
    private String deleteFlag;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 构造函数
    public ReimbursementItem() {
    }

    public ReimbursementItem(Long reimbursementId, String itemType, BigDecimal amount) {
        this.reimbursementId = reimbursementId;
        this.itemType = itemType;
        this.amount = amount;
        this.deleteFlag = "0";
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReimbursementId() {
        return reimbursementId;
    }

    public void setReimbursementId(Long reimbursementId) {
        this.reimbursementId = reimbursementId;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public Date getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(Date expenseDate) {
        this.expenseDate = expenseDate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReceiptUrl() {
        return receiptUrl;
    }

    public void setReceiptUrl(String receiptUrl) {
        this.receiptUrl = receiptUrl;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "ReimbursementItem{" +
                "id=" + id +
                ", reimbursementId=" + reimbursementId +
                ", itemType='" + itemType + '\'' +
                ", amount=" + amount +
                ", description='" + description + '\'' +
                '}';
    }
}
