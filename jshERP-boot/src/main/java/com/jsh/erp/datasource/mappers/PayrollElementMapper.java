package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.PayrollElement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 薪酬项目Mapper接口
 */
@Mapper
public interface PayrollElementMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(PayrollElement record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(PayrollElement record);

    /**
     * 根据主键查询
     */
    PayrollElement selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(PayrollElement record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(PayrollElement record);

    /**
     * 根据条件查询列表
     */
    List<PayrollElement> selectByCondition(@Param("elementName") String elementName,
                                          @Param("elementType") String elementType,
                                          @Param("status") String status,
                                          @Param("tenantId") Long tenantId,
                                          @Param("offset") Integer offset,
                                          @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("elementName") String elementName,
                         @Param("elementType") String elementType,
                         @Param("status") String status,
                         @Param("tenantId") Long tenantId);

    /**
     * 根据项目编码查询
     */
    PayrollElement selectByElementCode(@Param("elementCode") String elementCode,
                                      @Param("tenantId") Long tenantId);

    /**
     * 查询所有有效的薪酬项目
     */
    List<PayrollElement> selectAllActive(@Param("tenantId") Long tenantId);

    /**
     * 根据项目类型查询
     */
    List<PayrollElement> selectByElementType(@Param("elementType") String elementType,
                                           @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);

    /**
     * 检查项目编码是否存在
     */
    int checkElementCodeExists(@Param("elementCode") String elementCode,
                              @Param("tenantId") Long tenantId,
                              @Param("id") Long id);
}
