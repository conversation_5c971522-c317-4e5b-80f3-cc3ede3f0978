package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.SalaryCalculation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 薪酬计算记录扩展Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Mapper
public interface SalaryCalculationMapperEx {

    /**
     * 根据条件查询薪酬计算记录
     * 
     * @param employeeName 员工姓名
     * @param calculationMonth 计算月份
     * @param status 状态
     * @param tenantId 租户ID
     * @return 计算记录列表
     */
    List<SalaryCalculation> selectByCondition(@Param("employeeName") String employeeName,
                                             @Param("calculationMonth") String calculationMonth,
                                             @Param("status") String status,
                                             @Param("tenantId") Long tenantId);

    /**
     * 查询员工指定月份的薪酬计算记录
     * 
     * @param employeeId 员工ID
     * @param calculationMonth 计算月份
     * @param tenantId 租户ID
     * @return 计算记录
     */
    SalaryCalculation selectByEmployeeAndMonth(@Param("employeeId") Long employeeId,
                                              @Param("calculationMonth") String calculationMonth,
                                              @Param("tenantId") Long tenantId);

    /**
     * 统计指定月份的薪酬总额
     * 
     * @param calculationMonth 计算月份
     * @param tenantId 租户ID
     * @return 薪酬统计结果
     */
    Map<String, Object> sumByMonth(@Param("calculationMonth") String calculationMonth,
                                   @Param("tenantId") Long tenantId);

    /**
     * 查询待审批的薪酬计算记录
     * 
     * @param tenantId 租户ID
     * @return 待审批记录列表
     */
    List<SalaryCalculation> selectPendingApproval(@Param("tenantId") Long tenantId);

    /**
     * 统计员工年度薪酬
     * 
     * @param employeeId 员工ID
     * @param year 年份
     * @param tenantId 租户ID
     * @return 年度薪酬统计
     */
    List<Map<String, Object>> sumByEmployeeAndYear(@Param("employeeId") Long employeeId,
                                                   @Param("year") String year,
                                                   @Param("tenantId") Long tenantId);

    /**
     * 查询部门月度薪酬统计
     * 
     * @param calculationMonth 计算月份
     * @param tenantId 租户ID
     * @return 部门薪酬统计
     */
    List<Map<String, Object>> sumByDepartmentAndMonth(@Param("calculationMonth") String calculationMonth,
                                                      @Param("tenantId") Long tenantId);

    /**
     * 批量更新计算状态
     *
     * @param ids 计算记录ID列表
     * @param status 新状态
     * @param approverId 审批人ID
     * @param tenantId 租户ID
     * @return 更新记录数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") String status,
                         @Param("approverId") Long approverId,
                         @Param("tenantId") Long tenantId);

    // ========== 薪酬查询相关方法 ==========

    /**
     * 查询个人薪酬记录
     */
    List<Map<String, Object>> selectPersonalSalaryRecords(@Param("employeeId") Long employeeId,
                                                          @Param("calculationMonth") String calculationMonth,
                                                          @Param("year") String year,
                                                          @Param("tenantId") Long tenantId);

    /**
     * 查询个人薪酬详情
     */
    Map<String, Object> selectPersonalSalaryDetail(@Param("calculationId") Long calculationId,
                                                   @Param("employeeId") Long employeeId,
                                                   @Param("tenantId") Long tenantId);

    /**
     * 查询月度统计数据
     */
    List<Map<String, Object>> selectMonthlyStatistics(@Param("year") String year,
                                                      @Param("department") String department,
                                                      @Param("tenantId") Long tenantId);

    /**
     * 查询年度统计数据
     */
    Map<String, Object> selectYearlyStatistics(@Param("year") String year,
                                               @Param("department") String department,
                                               @Param("tenantId") Long tenantId);

    /**
     * 查询部门统计数据
     */
    List<Map<String, Object>> selectDepartmentStatistics(@Param("year") String year,
                                                         @Param("tenantId") Long tenantId);

    /**
     * 查询薪酬趋势数据
     */
    List<Map<String, Object>> selectSalaryTrend(@Param("employeeId") Long employeeId,
                                               @Param("startMonth") String startMonth,
                                               @Param("endMonth") String endMonth,
                                               @Param("tenantId") Long tenantId);

    /**
     * 查询部门薪酬对比
     */
    List<Map<String, Object>> selectDepartmentComparison(@Param("calculationMonth") String calculationMonth,
                                                         @Param("tenantId") Long tenantId);

    /**
     * 查询薪酬构成分析
     */
    Map<String, Object> selectSalaryComposition(@Param("calculationId") Long calculationId,
                                               @Param("tenantId") Long tenantId);

    /**
     * 查询个人薪酬记录用于导出
     */
    List<Map<String, Object>> selectPersonalSalaryForExport(@Param("employeeId") Long employeeId,
                                                            @Param("startMonth") String startMonth,
                                                            @Param("endMonth") String endMonth,
                                                            @Param("tenantId") Long tenantId);

    /**
     * 查询薪酬排行榜
     */
    List<Map<String, Object>> selectSalaryRanking(@Param("calculationMonth") String calculationMonth,
                                                 @Param("department") String department,
                                                 @Param("rankingType") String rankingType,
                                                 @Param("tenantId") Long tenantId);

    /**
     * 查询薪酬汇总报表
     */
    List<Map<String, Object>> selectSalarySummary(@Param("calculationMonth") String calculationMonth,
                                                 @Param("groupBy") String groupBy,
                                                 @Param("tenantId") Long tenantId);
}
