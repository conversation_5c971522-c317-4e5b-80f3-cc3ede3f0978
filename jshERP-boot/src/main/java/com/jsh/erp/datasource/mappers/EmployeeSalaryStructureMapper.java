package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.EmployeeSalaryStructure;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 员工薪资结构Mapper接口
 */
@Mapper
public interface EmployeeSalaryStructureMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(EmployeeSalaryStructure record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(EmployeeSalaryStructure record);

    /**
     * 根据主键查询
     */
    EmployeeSalaryStructure selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(EmployeeSalaryStructure record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(EmployeeSalaryStructure record);

    /**
     * 根据员工ID查询薪资结构
     */
    List<EmployeeSalaryStructure> selectByEmployeeId(@Param("employeeId") Long employeeId,
                                                   @Param("tenantId") Long tenantId);

    /**
     * 根据条件查询列表
     */
    List<EmployeeSalaryStructure> selectByCondition(@Param("employeeName") String employeeName,
                                                  @Param("elementName") String elementName,
                                                  @Param("status") String status,
                                                  @Param("tenantId") Long tenantId,
                                                  @Param("offset") Integer offset,
                                                  @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("employeeName") String employeeName,
                         @Param("elementName") String elementName,
                         @Param("status") String status,
                         @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);

    /**
     * 根据员工ID和薪酬项目ID查询
     */
    EmployeeSalaryStructure selectByEmployeeAndElement(@Param("employeeId") Long employeeId,
                                                     @Param("elementId") Long elementId,
                                                     @Param("tenantId") Long tenantId);

    /**
     * 检查员工薪酬项目是否已存在
     */
    int checkEmployeeElementExists(@Param("employeeId") Long employeeId,
                                  @Param("elementId") Long elementId,
                                  @Param("tenantId") Long tenantId,
                                  @Param("id") Long id);
}
