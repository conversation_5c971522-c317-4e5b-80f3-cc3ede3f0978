package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.PayslipDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 工资单明细Mapper接口
 */
@Mapper
public interface PayslipDetailsMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(PayslipDetails record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(PayslipDetails record);

    /**
     * 根据主键查询
     */
    PayslipDetails selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(PayslipDetails record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(PayslipDetails record);

    /**
     * 根据工资单ID查询明细列表
     */
    List<PayslipDetails> selectByPayslipId(@Param("payslipId") Long payslipId,
                                          @Param("tenantId") Long tenantId);

    /**
     * 根据条件查询列表
     */
    List<PayslipDetails> selectByCondition(@Param("payslipId") Long payslipId,
                                          @Param("elementName") String elementName,
                                          @Param("elementType") String elementType,
                                          @Param("tenantId") Long tenantId,
                                          @Param("offset") Integer offset,
                                          @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("payslipId") Long payslipId,
                         @Param("elementName") String elementName,
                         @Param("elementType") String elementType,
                         @Param("tenantId") Long tenantId);

    /**
     * 根据工资单ID删除所有明细
     */
    int deleteByPayslipId(@Param("payslipId") Long payslipId,
                         @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);
}
