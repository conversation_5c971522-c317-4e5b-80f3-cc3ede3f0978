package com.jsh.erp.datasource.mappers;

import com.jsh.erp.datasource.entities.Payslip;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 工资单主表Mapper接口
 */
@Mapper
public interface PayslipMapper {
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     */
    int insert(Payslip record);

    /**
     * 插入记录（选择性）
     */
    int insertSelective(Payslip record);

    /**
     * 根据主键查询
     */
    Payslip selectByPrimaryKey(Long id);

    /**
     * 根据主键更新（选择性）
     */
    int updateByPrimaryKeySelective(Payslip record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(Payslip record);

    /**
     * 根据员工ID和薪酬期间查询工资单
     */
    Payslip selectByEmployeeAndPeriod(@Param("employeeId") Long employeeId,
                                     @Param("payPeriod") String payPeriod,
                                     @Param("tenantId") Long tenantId);

    /**
     * 根据条件查询列表
     */
    List<Payslip> selectByCondition(@Param("employeeName") String employeeName,
                                   @Param("payPeriod") String payPeriod,
                                   @Param("status") String status,
                                   @Param("tenantId") Long tenantId,
                                   @Param("offset") Integer offset,
                                   @Param("rows") Integer rows);

    /**
     * 根据条件查询总数
     */
    Long countByCondition(@Param("employeeName") String employeeName,
                         @Param("payPeriod") String payPeriod,
                         @Param("status") String status,
                         @Param("tenantId") Long tenantId);

    /**
     * 批量删除
     */
    int batchDeleteByIds(@Param("ids") String ids,
                        @Param("tenantId") Long tenantId,
                        @Param("updater") Long updater,
                        @Param("updateTime") Date updateTime);

    /**
     * 更新工资单状态
     */
    int updateStatus(@Param("id") Long id,
                    @Param("status") String status,
                    @Param("tenantId") Long tenantId,
                    @Param("updater") Long updater,
                    @Param("updateTime") Date updateTime);
}
